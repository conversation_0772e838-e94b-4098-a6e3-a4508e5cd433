import { authenticateAdminRequest } from '@/lib/admin-auth';
import fs from 'fs';
import path from 'path';

/**
 * API endpoint for managing Google Search Console verification files
 * 
 * GET: List existing Google verification files
 * DELETE: Remove a specific Google verification file
 */
export default async function handler(req, res) {
  // Authenticate admin request
  const authResult = await authenticateAdminRequest(req);
  if (!authResult.success) {
    return res.status(401).json({ 
      error: 'Unauthorized', 
      message: authResult.message 
    });
  }

  const publicDir = path.join(process.cwd(), 'public');
  const googleVerificationPattern = /^google[a-f0-9]+\.html$/i;

  try {
    if (req.method === 'GET') {
      // List existing Google verification files
      const files = fs.readdirSync(publicDir)
        .filter(filename => googleVerificationPattern.test(filename))
        .map(filename => {
          const filePath = path.join(publicDir, filename);
          const stats = fs.statSync(filePath);
          
          return {
            filename,
            url: `/${filename}`,
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime
          };
        });

      return res.status(200).json({
        success: true,
        files,
        count: files.length
      });

    } else if (req.method === 'DELETE') {
      // Delete a specific Google verification file
      const { filename } = req.query;

      if (!filename) {
        return res.status(400).json({ error: 'Filename is required' });
      }

      if (!googleVerificationPattern.test(filename)) {
        return res.status(400).json({ error: 'Invalid filename format' });
      }

      const filePath = path.join(publicDir, filename);

      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ error: 'File not found' });
      }

      try {
        fs.unlinkSync(filePath);
        console.log(`Deleted Google verification file: ${filename}`);

        return res.status(200).json({
          success: true,
          message: `File ${filename} deleted successfully`
        });
      } catch (error) {
        console.error(`Error deleting file ${filename}:`, error);
        return res.status(500).json({ error: 'Failed to delete file' });
      }

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error('Error managing Google verification files:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      message: 'Failed to manage verification files' 
    });
  }
}
