import { authenticateAdminRequest } from '@/lib/admin-auth';
import nodemailer from 'nodemailer';

/**
 * API endpoint for testing various service connections
 * Tests Square API, Google Analytics, Email SMTP, etc.
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Connection test API called`);

  // Authenticate request
  const authResult = await authenticateAdminRequest(req);
  const { authorized, error, user, role } = authResult;

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error');
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    });
  }

  // Only allow admin users
  if (role !== 'admin') {
    console.error(`[${requestId}] User ${user?.email} with role ${role} attempted to test connections`);
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Only admin users can test connections',
      requestId
    });
  }

  const { type, settings } = req.body;

  try {
    let result = { success: false, message: '', details: {} };

    switch (type) {
      case 'square':
        result = await testSquareConnection(settings);
        break;
      
      case 'google_analytics':
        result = await testGoogleAnalytics(settings);
        break;
      
      case 'email':
        result = await testEmailConnection(settings);
        break;
      
      case 'google_business':
        result = await testGoogleBusiness(settings);
        break;
      
      default:
        return res.status(400).json({
          error: 'Invalid connection type',
          message: `Unsupported connection type: ${type}`,
          requestId
        });
    }

    console.log(`[${requestId}] Connection test result for ${type}:`, result.success ? 'SUCCESS' : 'FAILED');
    
    return res.status(200).json({
      ...result,
      requestId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`[${requestId}] Connection test error:`, error);
    return res.status(500).json({
      error: 'Connection test failed',
      message: error.message,
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Test Square API connection
 */
async function testSquareConnection(settings) {
  try {
    const { application_id, access_token, environment, location_id } = settings;

    if (!application_id || !access_token) {
      return {
        success: false,
        message: 'Missing required Square API credentials',
        details: { missing: ['application_id', 'access_token'].filter(key => !settings[key]) }
      };
    }

    // Determine the base URL based on environment
    const baseUrl = environment === 'production' 
      ? 'https://connect.squareup.com'
      : 'https://connect.squareupsandbox.com';

    // Test with a simple locations API call
    const response = await fetch(`${baseUrl}/v2/locations`, {
      method: 'GET',
      headers: {
        'Square-Version': '2023-10-18',
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        message: `Square API error: ${response.status}`,
        details: { 
          status: response.status,
          error: errorData.errors?.[0]?.detail || 'Unknown error'
        }
      };
    }

    const data = await response.json();
    const locations = data.locations || [];

    // Check if the specified location_id exists (if provided)
    if (location_id) {
      const locationExists = locations.some(loc => loc.id === location_id);
      if (!locationExists) {
        return {
          success: false,
          message: 'Specified location ID not found',
          details: { 
            available_locations: locations.map(loc => ({ id: loc.id, name: loc.name }))
          }
        };
      }
    }

    return {
      success: true,
      message: 'Square API connection successful',
      details: {
        locations_count: locations.length,
        environment,
        available_locations: locations.map(loc => ({ id: loc.id, name: loc.name }))
      }
    };

  } catch (error) {
    return {
      success: false,
      message: 'Square API connection failed',
      details: { error: error.message }
    };
  }
}

/**
 * Test Google Analytics connection
 */
async function testGoogleAnalytics(settings) {
  try {
    const { measurement_id } = settings;

    if (!measurement_id) {
      return {
        success: false,
        message: 'Missing Google Analytics Measurement ID',
        details: {}
      };
    }

    // Validate measurement ID format
    const gaIdPattern = /^G-[A-Z0-9]{10}$/;
    if (!gaIdPattern.test(measurement_id)) {
      return {
        success: false,
        message: 'Invalid Google Analytics Measurement ID format',
        details: { 
          expected_format: 'G-XXXXXXXXXX',
          provided: measurement_id
        }
      };
    }

    // For now, we'll just validate the format since testing GA4 requires more complex setup
    // In a full implementation, you would use the Google Analytics Reporting API
    return {
      success: true,
      message: 'Google Analytics ID format is valid',
      details: {
        measurement_id,
        note: 'Full connection testing requires Google Analytics Reporting API setup'
      }
    };

  } catch (error) {
    return {
      success: false,
      message: 'Google Analytics test failed',
      details: { error: error.message }
    };
  }
}

/**
 * Test email SMTP connection
 */
async function testEmailConnection(settings) {
  try {
    const { smtp_host, smtp_port, smtp_username, smtp_password, smtp_encryption } = settings;

    if (!smtp_host || !smtp_username) {
      return {
        success: false,
        message: 'Missing required SMTP settings',
        details: { 
          missing: ['smtp_host', 'smtp_username'].filter(key => !settings[key])
        }
      };    }

    // Test actual SMTP connection using nodemailer
    try {
      const transporter = nodemailer.createTransporter({
        host: smtp_host,
        port: portNum,
        secure: smtp_encryption === 'ssl', // true for 465, false for other ports
        auth: {
          user: smtp_username,
          pass: smtp_password,
        },
        // Additional options for better compatibility
        tls: {
          rejectUnauthorized: false // For development/testing
        }
      });

      // Verify SMTP connection
      await transporter.verify();

      return {
        success: true,
        message: 'SMTP connection test successful',
        details: {
          host: smtp_host,
          port: portNum,
          encryption: smtp_encryption,
          username: smtp_username
        }
      };

    } catch (connectionError) {
      return {
        success: false,
        message: 'SMTP connection failed',
        details: { 
          error: connectionError.message,
          host: smtp_host,
          port: portNum
        }
      };
    }

  } catch (error) {
    return {
      success: false,
      message: 'Email connection test failed',
      details: { error: error.message }
    };
  }
}

/**
 * Test Google Business Profile connection
 */
async function testGoogleBusiness(settings) {
  try {
    const { business_profile_id, api_key } = settings;

    if (!business_profile_id || !api_key) {
      return {
        success: false,
        message: 'Missing Google Business Profile credentials',
        details: {
          missing: ['business_profile_id', 'api_key'].filter(key => !settings[key])
        }
      };
    }

    // Validate business profile ID format
    const profileIdPattern = /^accounts\/\d+\/locations\/\d+$/;
    if (!profileIdPattern.test(business_profile_id)) {
      return {
        success: false,
        message: 'Invalid Google Business Profile ID format',
        details: {
          expected_format: 'accounts/123456789012345678901/locations/987654321098765432109',
          provided: business_profile_id
        }
      };
    }

    // Note: In a production environment, you would test the actual Google My Business API connection here
    return {
      success: true,
      message: 'Google Business Profile ID format is valid',
      details: {
        profile_id: business_profile_id,
        note: 'Full connection testing requires Google My Business API implementation'
      }
    };

  } catch (error) {
    return {
      success: false,
      message: 'Google Business Profile test failed',
      details: { error: error.message }
    };
  }
}
