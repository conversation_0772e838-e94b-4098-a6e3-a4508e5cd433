// Orders API endpoint for public-facing orders
import supabase, { getAdminClient } from '@/lib/supabase';
import { sendOrderNotification } from '@/lib/notifications';

/**
 * Public API endpoint for handling order submissions
 * This endpoint uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Set CORS headers for API routes
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Route based on HTTP method
  switch (req.method) {
    case 'POST':
      return await createOrder(req, res);
    case 'GET':
      return await getOrder(req, res);
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

/**
 * Create a new order
 */
async function createOrder(req, res) {
  try {
    const { customer_id, items, subtotal, shipping, total, status = 'PENDING' } = req.body;

    // Validate required fields
    if (!customer_id || !items || items.length === 0 || subtotal === undefined || total === undefined) {
      return res.status(400).json({ error: 'Missing required fields' });
    }    // Get admin client
    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Admin client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Ensure customer exists
    const { data: customer, error: customerError } = await adminClient
      .from('customers')
      .select('*')
      .eq('id', customer_id)
      .single();

    if (customerError) {
      console.error('Error fetching customer:', customerError);
      return res.status(400).json({ error: 'Invalid customer ID' });
    }    // Create order
    const { data: order, error: orderError } = await adminClient
      .from('orders')
      .insert([{
        customer_id,
        order_date: new Date().toISOString(),
        subtotal,
        shipping,
        total,
        status
      }])
      .select();

    if (orderError) {
      console.error('Error creating order:', orderError);
      return res.status(500).json({ error: 'Failed to create order' });
    }

    // Add order items
    const orderItems = items.map(item => ({
      order_id: order[0].id,
      product_id: item.id,
      quantity: item.quantity,
      price: item.price,
      name: item.name
    }));    const { error: orderItemsError } = await adminClient
      .from('order_items')
      .insert(orderItems);

    if (orderItemsError) {
      console.error('Error adding order items:', orderItemsError);
      return res.status(500).json({ error: 'Failed to add order items' });
    }

    // Send order notification
    try {
      await sendOrderNotification({
        orderId: order[0].id,
        customerEmail: customer.email,
        customerName: customer.name,
        status: order[0].status,
        total: total,
        items: items,
        marketingConsent: customer.marketing_consent
      });
    } catch (notificationError) {
      console.error('Error sending notification:', notificationError);
      // Continue anyway since the order was created successfully
    }

    return res.status(201).json({
      success: true,
      message: 'Order created successfully',
      order: {
        id: order[0].id,
        status: order[0].status,
        date: order[0].order_date,
        total: order[0].total
      }
    });
  } catch (error) {
    console.error('Order creation error:', error);
    return res.status(500).json({
      error: 'Failed to create order',
      details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}

/**
 * Get order details
 */
async function getOrder(req, res) {
  try {
    const { id, email } = req.query;

    if (!id) {
      return res.status(400).json({ error: 'Order ID is required' });
    }    // Get admin client if not already initialized
    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Admin client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    let query = adminClient
      .from('orders')
      .select(`
        *,
        customers (id, name, email),
        order_items (id, product_id, quantity, price, name)
      `)
      .eq('id', id);

    // If email is provided, verify it matches the customer's email for security
    if (email) {
      query = query.eq('customers.email', email);
    }

    const { data: order, error } = await query.single();

    if (error) {
      console.error('Error fetching order:', error);
      return res.status(404).json({ error: 'Order not found' });
    }

    return res.status(200).json({
      order
    });
  } catch (error) {
    console.error('Order fetch error:', error);
    return res.status(500).json({
      error: 'Failed to fetch order',
      details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}
