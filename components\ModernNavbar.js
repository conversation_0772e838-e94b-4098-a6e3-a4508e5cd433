import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '@/styles/ModernNavbar.module.css';
import { safeRender } from '@/lib/safe-render-utils';

/**
 * ModernNavbar component with scroll effects and mobile menu
 *
 * @param {Object} props - Component props
 * @param {string} props.logo - URL of the logo image
 * @param {Array} props.menuItems - Array of menu items with label and href
 * @returns {JSX.Element}
 */
const ModernNavbar = ({
  logo = '/publicimages\bannerlogo.PNG',
  menuItems = [
    { label: 'Home', href: '/' },
    { label: 'About', href: '/about' },
    { label: 'Services', href: '/services' },
    { label: 'Shop', href: '/shop' },
    { label: 'Contact', href: '/contact' },
  ]
}) => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const router = useRouter();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    const handleRouteChange = () => {
      setMobileMenuOpen(false);
    };

    router.events.on('routeChangeComplete', handleRouteChange);

    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [router.events]);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className={`${styles.navbar} ${scrolled ? styles.scrolled : ''}`}>
      <div className={styles.navbarContainer}>
        <Link href="/" className={styles.logoContainer}>
          <img src={safeRender(logo, '/publicimages/bannerlogo.PNG')} alt="Ocean Soul Sparkles" className={styles.logo} />
        </Link>

        <nav className={styles.desktopNav}>
          <ul className={styles.navList}>
            {menuItems.map((item, index) => (
              <li key={index} className={styles.navItem}>
                <Link
                  href={safeRender(item.href, '/')}
                  className={`${styles.navLink} ${router.pathname === safeRender(item.href, '/') ? styles.active : ''}`}
                >
                  {safeRender(item.label)}
                  <span className={styles.navLinkUnderline}></span>
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <Link href="/book-online" className={`${styles.bookButton} button`}>
          Book Now
        </Link>

        <button
          className={`${styles.mobileMenuButton} ${mobileMenuOpen ? styles.open : ''}`}
          onClick={toggleMobileMenu}
          aria-label="Toggle mobile menu"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>

      <div className={`${styles.mobileMenu} ${mobileMenuOpen ? styles.open : ''}`}>
        <nav>
          <ul className={styles.mobileNavList}>
            {menuItems.map((item, index) => (
              <li key={index} className={styles.mobileNavItem}>
                <Link
                  href={safeRender(item.href, '/')}
                  className={`${styles.mobileNavLink} ${router.pathname === safeRender(item.href, '/') ? styles.active : ''}`}
                >
                  {safeRender(item.label)}
                </Link>
              </li>
            ))}
            <li className={styles.mobileNavItem}>
              <Link href="/book-online" className={styles.mobileBookButton}>
                Book Now
              </Link>
            </li>
          </ul>
        </nav>

        <div className={styles.mobileMenuFooter}>
          <div className={styles.socialLinks}>
            <a href="https://www.instagram.com/oceansoulsparkles" target="_blank" rel="noopener noreferrer" className={styles.socialLink}>
              <img src="/images/social/instagram-icon.png" alt="Instagram" />
            </a>
            <a href="https://www.facebook.com/OceanSoulSparkles/" target="_blank" rel="noopener noreferrer" className={styles.socialLink}>
              <img src="/images/social/facebook-icon.png" alt="Facebook" />
            </a>
          </div>
        </div>
      </div>
    </header>
  );
};

export default ModernNavbar;
