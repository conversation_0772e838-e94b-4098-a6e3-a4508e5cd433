import { useState } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import CustomerBooking<PERSON><PERSON><PERSON> from './CustomerBookingHistory'
import Modal from './Modal'
import LoadingButton from './LoadingButton'
import styles from '@/styles/admin/CustomerDetails.module.css'
import { useAuth } from '@/contexts/AuthContext'
import { authenticatedFetch } from '@/lib/auth-utils'

export default function CustomerDetails({ customer, bookings, preferences }) {
  const router = useRouter()
  const { isAdmin } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showGdprModal, setShowGdprModal] = useState(false)
  const [gdprReason, setGdprReason] = useState('')
  const [showNotificationModal, setShowNotificationModal] = useState(false)
  const [notificationData, setNotificationData] = useState({
    title: '',
    message: '',
    type: 'email'
  })

  if (!customer) {
    return <div className={styles.loading}>Loading customer details...</div>
  }

  const handleDelete = async () => {
    setLoading(true)
    setError(null)

    try {
      // Use authenticatedFetch for the DELETE request
      await authenticatedFetch(`/api/customers/${customer.id}`, {
        method: 'DELETE'
      });

      setSuccessMessage('Customer deleted successfully');

      // Navigate back to customer list after a short delay
      setTimeout(() => {
        router.push('/admin/customers');
      }, 1500);
    } catch (error) {
      console.error('Error deleting customer:', error);
      setError(error.message);
    } finally {
      setLoading(false);
      setShowDeleteModal(false);
    }
  }

  const handleGdprDelete = async () => {
    setLoading(true)
    setError(null)

    try {
      // Use authenticatedFetch for the GDPR delete request
      await authenticatedFetch(`/api/customers/${customer.id}/gdpr-delete`, {
        method: 'POST',
        body: JSON.stringify({
          reason: gdprReason || 'Customer request'
        })
      });

      setSuccessMessage('Customer data anonymized successfully');

      // Navigate back to customer list after a short delay
      setTimeout(() => {
        router.push('/admin/customers');
      }, 1500);
    } catch (error) {
      console.error('Error processing GDPR deletion:', error);
      setError(error.message);
    } finally {
      setLoading(false);
      setShowGdprModal(false);
    }
  }

  const handleNotificationChange = (e) => {
    const { name, value } = e.target
    setNotificationData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const sendNotification = async () => {
    if (!notificationData.title || !notificationData.message) {
      setError('Notification title and message are required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Use authenticatedFetch for sending notifications
      await authenticatedFetch('/api/notifications/send', {
        method: 'POST',
        body: JSON.stringify({
          customer_id: customer.id,
          title: notificationData.title,
          message: notificationData.message,
          type: notificationData.type
        })
      });

      setSuccessMessage('Notification sent successfully');
      setShowNotificationModal(false);
      setNotificationData({
        title: '',
        message: '',
        type: 'email'
      });
    } catch (error) {
      console.error('Error sending notification:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className={styles.customerDetails}>
      {error && <div className={styles.error}>{error}</div>}
      {successMessage && <div className={styles.success}>{successMessage}</div>}

      <div className={styles.header}>
        <h2>{customer.name}</h2>
        <div className={styles.actions}>
          <button
            className={styles.editButton}
            onClick={() => router.push(`/admin/customers/${customer.id}/edit`)}
            disabled={loading}
          >
            Edit
          </button>
          {isAdmin && (
            <button
              className={styles.deleteButton}
              onClick={() => setShowDeleteModal(true)}
              disabled={loading}
            >
              Delete
            </button>
          )}
        </div>
      </div>

      <div className={styles.content}>
        <div className={styles.customerInfo}>
          <div className={styles.infoSection}>
            <h3>Contact Information</h3>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Email:</span>
                <span className={styles.infoValue}>
                  <a href={`mailto:${customer.email}`}>{customer.email}</a>
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Phone:</span>
                <span className={styles.infoValue}>
                  {customer.phone ? (
                    <a href={`tel:${customer.phone}`}>{customer.phone}</a>
                  ) : (
                    'Not provided'
                  )}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Address:</span>
                <span className={styles.infoValue}>
                  {customer.address ? (
                    <>
                      {customer.address}<br />
                      {customer.city && `${customer.city}, `}
                      {customer.state && `${customer.state} `}
                      {customer.postal_code && customer.postal_code}
                      {customer.country && <><br />{customer.country}</>}
                    </>
                  ) : (
                    'Not provided'
                  )}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Created:</span>
                <span className={styles.infoValue}>
                  {new Date(customer.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {preferences && preferences.length > 0 && (
            <div className={styles.infoSection}>
              <h3>Customer Preferences</h3>
              <div className={styles.preferencesList}>
                {preferences.map((pref, index) => (
                  <div key={index} className={styles.preferenceItem}>
                    <span className={styles.preferenceKey}>{pref.key}:</span>
                    <span className={styles.preferenceValue}>{pref.value}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {customer.notes && (
            <div className={styles.infoSection}>
              <h3>Notes</h3>
              <div className={styles.notes}>
                {customer.notes}
              </div>
            </div>
          )}

          <div className={styles.infoSection}>
            <h3>Marketing</h3>
            <div className={styles.marketingConsent}>
              <span className={styles.consentStatus}>
                {customer.marketing_consent ? (
                  <span className={styles.consentGranted}>Opted In</span>
                ) : (
                  <span className={styles.consentDenied}>Opted Out</span>
                )}
              </span>
              <p className={styles.consentDescription}>
                {customer.marketing_consent
                  ? 'Customer has consented to receive marketing communications.'
                  : 'Customer has not consented to receive marketing communications.'}
              </p>
              <button
                className={styles.notificationButton}
                onClick={() => setShowNotificationModal(true)}
                disabled={loading || !customer.email}
              >
                Send Notification
              </button>
            </div>
          </div>

          {isAdmin && (
            <div className={styles.infoSection}>
              <h3>GDPR Compliance</h3>
              <button
                className={styles.gdprButton}
                onClick={() => setShowGdprModal(true)}
                disabled={loading}
              >
                Anonymize Customer Data
              </button>
              <p className={styles.gdprDescription}>
                This will anonymize all personal data while preserving booking history.
                This action cannot be undone.
              </p>
            </div>
          )}
        </div>

        <div className={styles.bookingHistory}>
          <CustomerBookingHistory bookings={bookings} />
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <Modal onClose={() => setShowDeleteModal(false)}>
          <div className={styles.modalContent}>
            <h3>Confirm Deletion</h3>
            <p>Are you sure you want to delete this customer? This action cannot be undone.</p>
            <div className={styles.modalActions}>
              <LoadingButton
                type="button"
                variant="secondary"
                className={styles.cancelButton}
                onClick={() => setShowDeleteModal(false)}
                loading={loading}
              >
                Cancel
              </LoadingButton>
              <LoadingButton
                type="button"
                variant="danger"
                className={styles.deleteButton}
                onClick={handleDelete}
                loading={loading}
                loadingText="Deleting..."
              >
                Delete Customer
              </LoadingButton>
            </div>
          </div>
        </Modal>
      )}

      {/* GDPR Deletion Modal */}
      {showGdprModal && (
        <Modal onClose={() => setShowGdprModal(false)}>
          <div className={styles.modalContent}>
            <h3>GDPR Data Anonymization</h3>
            <p>
              This will anonymize all personal data for this customer while preserving booking history.
              This action is designed for GDPR compliance and cannot be undone.
            </p>
            <div className={styles.formGroup}>
              <label htmlFor="gdprReason">Reason for Anonymization:</label>
              <input
                type="text"
                id="gdprReason"
                value={gdprReason}
                onChange={(e) => setGdprReason(e.target.value)}
                placeholder="e.g., Customer request, Data retention policy"
                className={styles.input}
              />
            </div>
            <div className={styles.modalActions}>
              <LoadingButton
                type="button"
                variant="secondary"
                className={styles.cancelButton}
                onClick={() => setShowGdprModal(false)}
                loading={loading}
              >
                Cancel
              </LoadingButton>
              <LoadingButton
                type="button"
                variant="primary"
                className={styles.gdprButton}
                onClick={handleGdprDelete}
                loading={loading}
                loadingText="Processing..."
              >
                Anonymize Data
              </LoadingButton>
            </div>
          </div>
        </Modal>
      )}

      {/* Notification Modal */}
      {showNotificationModal && (
        <Modal onClose={() => setShowNotificationModal(false)}>
          <div className={styles.modalContent}>
            <h3>Send Notification to Customer</h3>
            <p>
              Send a notification to {customer.name} via email or in-app notification.
            </p>
            <div className={styles.formGroup}>
              <label htmlFor="type">Notification Type:</label>
              <select
                id="type"
                name="type"
                value={notificationData.type}
                onChange={handleNotificationChange}
                className={styles.select}
                disabled={loading}
              >
                <option value="email">Email</option>
                <option value="sms">SMS</option>
                <option value="push">Push Notification</option>
              </select>
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="title">Title:</label>
              <input
                type="text"
                id="title"
                name="title"
                value={notificationData.title}
                onChange={handleNotificationChange}
                placeholder="Notification title"
                className={styles.input}
                disabled={loading}
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="message">Message:</label>
              <textarea
                id="message"
                name="message"
                value={notificationData.message}
                onChange={handleNotificationChange}
                placeholder="Enter your message here..."
                className={styles.textarea}
                rows={4}
                disabled={loading}
              />
            </div>
            <div className={styles.modalActions}>
              <LoadingButton
                type="button"
                variant="secondary"
                className={styles.cancelButton}
                onClick={() => setShowNotificationModal(false)}
                loading={loading}
              >
                Cancel
              </LoadingButton>
              <LoadingButton
                type="button"
                variant="success"
                className={styles.sendButton}
                onClick={sendNotification}
                loading={loading}
                loadingText="Sending..."
                disabled={!notificationData.title || !notificationData.message}
              >
                Send Notification
              </LoadingButton>
            </div>
          </div>
        </Modal>
      )}
    </div>
  )
}
