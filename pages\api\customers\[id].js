import { supabaseAdmin } from '@/lib/supabase';
import * as authTokenManager from '@/lib/auth-token-manager';

export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Customer API request: ${req.method} ${req.url}`);

  // Check authentication using the new auth token manager
  const authResult = await authTokenManager.verifyToken(req);

  if (!authResult.valid) {
    console.error(`[${requestId}] Authentication failed:`, authResult.error);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authResult.error || 'Authentication failed',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${authResult.user?.email}, Role: ${authResult.user?.role}`);

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getCustomer(id, res, requestId)
    case 'PUT':
      return updateCustomer(id, req, res, requestId)
    case 'DELETE':
      // Only admins can delete customers
      if (authResult.user?.role !== 'admin') {
        console.error(`[${requestId}] Delete forbidden for role: ${authResult.user?.role}`);
        return res.status(403).json({
          error: 'Forbidden',
          message: 'Only administrators can delete customers',
          requestId
        });
      }
      return deleteCustomer(id, res, requestId)
    default:
      return res.status(405).json({
        error: 'Method not allowed',
        requestId
      })
  }
}

// Get a single customer with their booking history
async function getCustomer(id, res, requestId) {
  try {
    console.log(`[${requestId}] Fetching customer: ${id}`);

    // Get customer details using admin client
    const { data: customer, error } = await supabaseAdmin
      .from('customers')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error(`[${requestId}] Error fetching customer:`, error);
      throw error
    }

    if (!customer) {
      console.log(`[${requestId}] Customer not found: ${id}`);
      return res.status(404).json({
        error: 'Customer not found',
        requestId
      })
    }

    // Get customer booking history
    const { data: bookings, error: bookingsError } = await supabaseAdmin
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        location,
        notes,
        services:service_id (id, name, price, duration, color)
      `)
      .eq('customer_id', id)
      .order('start_time', { ascending: false })

    if (bookingsError) {
      console.error(`[${requestId}] Error fetching bookings:`, bookingsError);
      throw bookingsError
    }

    // Get customer preferences
    const { data: preferences, error: preferencesError } = await supabaseAdmin
      .from('customer_preferences')
      .select('*')
      .eq('customer_id', id)

    if (preferencesError) {
      console.error(`[${requestId}] Error fetching preferences:`, preferencesError);
      throw preferencesError
    }

    console.log(`[${requestId}] Successfully fetched customer with ${bookings?.length || 0} bookings`);

    // Return customer with booking history and preferences
    return res.status(200).json({
      customer,
      bookings: bookings || [],
      preferences: preferences || [],
      requestId
    })
  } catch (error) {
    console.error(`[${requestId}] Error fetching customer:`, error)
    return res.status(error.code === 'PGRST116' ? 404 : 500).json({
      error: error.code === 'PGRST116' ? 'Customer not found' : 'Failed to fetch customer',
      requestId
    })
  }
}

// Update a customer
async function updateCustomer(id, req, res, requestId) {
  try {
    console.log(`[${requestId}] Updating customer: ${id}`);

    const {
      name,
      email,
      phone,
      address,
      city,
      state,
      postal_code,
      country,
      notes,
      marketing_consent,
      preferences
    } = req.body

    // Validate required fields
    if (!name || !email) {
      console.log(`[${requestId}] Validation failed: missing name or email`);
      return res.status(400).json({
        error: 'Name and email are required',
        requestId
      })
    }

    // Check if email already exists (for a different customer)
    const { data: existingCustomer, error: checkError } = await supabaseAdmin
      .from('customers')
      .select('id')
      .eq('email', email)
      .neq('id', id)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error(`[${requestId}] Error checking existing customer:`, checkError);
      throw checkError
    }

    if (existingCustomer) {
      console.log(`[${requestId}] Email conflict: ${email} already exists for customer ${existingCustomer.id}`);
      return res.status(409).json({
        error: 'Another customer with this email already exists',
        requestId
      })
    }

    // Update customer
    const { data, error } = await supabaseAdmin
      .from('customers')
      .update({
        name,
        email,
        phone,
        address,
        city,
        state,
        postal_code,
        country,
        notes,
        marketing_consent,
        updated_at: new Date()
      })
      .eq('id', id)
      .select()

    if (error) {
      console.error(`[${requestId}] Error updating customer:`, error);
      throw error
    }

    if (!data || data.length === 0) {
      console.log(`[${requestId}] Customer not found during update: ${id}`);
      return res.status(404).json({
        error: 'Customer not found',
        requestId
      })
    }

    // Update preferences if provided
    if (preferences && Array.isArray(preferences)) {
      console.log(`[${requestId}] Updating ${preferences.length} preferences`);

      // Delete existing preferences
      await supabaseAdmin
        .from('customer_preferences')
        .delete()
        .eq('customer_id', id)

      // Insert new preferences
      if (preferences.length > 0) {
        const preferencesData = preferences.map(pref => ({
          customer_id: id,
          preference_key: pref.key,
          preference_value: pref.value
        }))

        const { error: prefError } = await supabaseAdmin
          .from('customer_preferences')
          .insert(preferencesData)

        if (prefError) {
          console.error(`[${requestId}] Error updating preferences:`, prefError);
          throw prefError
        }
      }
    }

    console.log(`[${requestId}] Customer updated successfully: ${data[0].name}`);
    return res.status(200).json({
      ...data[0],
      requestId
    })
  } catch (error) {
    console.error(`[${requestId}] Error updating customer:`, error)
    return res.status(500).json({
      error: 'Failed to update customer',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while updating the customer',
      requestId
    })
  }
}

// Delete a customer
async function deleteCustomer(id, res, requestId) {
  try {
    console.log(`[${requestId}] Deleting customer: ${id}`);

    // Delete customer (preferences will be deleted via cascade)
    const { data, error } = await supabaseAdmin
      .from('customers')
      .delete()
      .eq('id', id)
      .select()

    if (error) {
      console.error(`[${requestId}] Error deleting customer:`, error);
      throw error
    }

    if (!data || data.length === 0) {
      console.log(`[${requestId}] Customer not found for deletion: ${id}`);
      return res.status(404).json({
        error: 'Customer not found',
        requestId
      })
    }

    console.log(`[${requestId}] Customer deleted successfully: ${data[0].name}`);
    return res.status(200).json({
      message: 'Customer deleted successfully',
      requestId
    })
  } catch (error) {
    console.error(`[${requestId}] Error deleting customer:`, error)
    return res.status(500).json({
      error: 'Failed to delete customer',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while deleting the customer',
      requestId
    })
  }
}
