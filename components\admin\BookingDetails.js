import { useState, useEffect } from 'react';
import { sendBookingNotification } from '@/lib/notifications';
import { STATUS_DISPLAY_NAMES } from '@/lib/booking-status';
import { formatDateTime } from '@/lib/booking-utils';
import BookingStatusSelector from './BookingStatusSelector';
import BookingStatusHistory from './BookingStatusHistory';
import styles from '@/styles/admin/BookingDetails.module.css';

export default function BookingDetails({ booking, onClose, onEdit, onUpdate }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  if (!booking) {
    return null;
  }

  const handleCancelBooking = async () => {
    if (!confirm('Are you sure you want to cancel this booking?')) {
      return;
    }

    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Update booking status to canceled via API
      const updateResponse = await fetch(`/api/admin/bookings?id=${booking.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customer_id: booking.customer_id,
          service_id: booking.service_id,
          start_time: booking.start_time,
          end_time: booking.end_time,
          status: 'canceled',
          location: booking.location || '',
          notes: booking.notes || ''
        }),
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        throw new Error(errorData.error || 'Failed to cancel booking');
      }

      // Send notification
      await sendBookingNotification({
        bookingId: booking.id,
        customerId: booking.customer_id,
        status: 'canceled',
        startTime: booking.start_time,
        serviceName: booking.serviceName
      });

      setSuccessMessage('Booking canceled successfully');

      // Call the onUpdate callback to refresh the calendar
      if (onUpdate) {
        onUpdate();
      }
    } catch (error) {
      console.error('Error canceling booking:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle status change
  const handleStatusChange = async (newStatus, notes) => {
    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Update booking status via dedicated status API
      const updateResponse = await fetch('/api/admin/bookings/status', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: booking.id,
          status: newStatus,
          notes: notes || ''
        }),
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        throw new Error(errorData.error || 'Failed to update booking status');
      }

      // Send notification
      await sendBookingNotification({
        bookingId: booking.id,
        customerId: booking.customer_id,
        status: newStatus,
        startTime: booking.start_time,
        serviceName: booking.serviceName
      });

      setSuccessMessage(`Booking status updated to ${STATUS_DISPLAY_NAMES[newStatus]}`);

      // Call the onUpdate callback to refresh the calendar
      if (onUpdate) {
        onUpdate();
      }
    } catch (error) {
      console.error('Error updating booking status:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 'confirmed':
        return styles.confirmed;
      case 'pending':
        return styles.pending;
      case 'canceled':
        return styles.canceled;
      case 'in_progress':
        return styles.inProgress;
      case 'completed':
        return styles.completed;
      case 'no_show':
        return styles.noShow;
      case 'rescheduled':
        return styles.rescheduled;
      default:
        return '';
    }
  };

  return (
    <div className={styles.bookingDetails}>
      <div className={styles.header}>
        <h2>Booking Details</h2>
      </div>

      {error && <div className={styles.error}>{error}</div>}
      {successMessage && <div className={styles.success}>{successMessage}</div>}

      {
        <div className={styles.bookingInfo}>
          <div className={styles.serviceInfo}>
            <div
              className={styles.serviceColor}
              style={{ backgroundColor: booking.serviceColor }}
            ></div>
            <span className={styles.serviceName}>{booking.serviceName}</span>
          </div>

          <div className={styles.statusBadge}>
            <span className={`${styles.status} ${getStatusClass(booking.status)}`}>
              {STATUS_DISPLAY_NAMES[booking.status] || booking.status}
            </span>
          </div>

          <div className={styles.statusManagement}>
            <h3>Status Management</h3>
            <BookingStatusSelector
              currentStatus={booking.status}
              onStatusChange={handleStatusChange}
              disabled={loading}
            />
          </div>

          <div className={styles.infoSection}>
            <h3>Customer Information</h3>
            <div className={styles.infoRow}>
              <span className={styles.infoLabel}>Name:</span>
              <span className={styles.infoValue}>{booking.customerName}</span>
            </div>
            <div className={styles.infoRow}>
              <span className={styles.infoLabel}>Email:</span>
              <span className={styles.infoValue}>{booking.customerEmail}</span>
            </div>
            <div className={styles.infoRow}>
              <span className={styles.infoLabel}>Phone:</span>
              <span className={styles.infoValue}>{booking.customerPhone || 'N/A'}</span>
            </div>
          </div>

          <div className={styles.infoSection}>
            <h3>Booking Information</h3>
            <div className={styles.infoRow}>
              <span className={styles.infoLabel}>Start:</span>
              <span className={styles.infoValue}>{formatDateTime(booking.start_time)}</span>
            </div>
            <div className={styles.infoRow}>
              <span className={styles.infoLabel}>End:</span>
              <span className={styles.infoValue}>{formatDateTime(booking.end_time)}</span>
            </div>
            <div className={styles.infoRow}>
              <span className={styles.infoLabel}>Location:</span>
              <span className={styles.infoValue}>{booking.location || 'N/A'}</span>
            </div>
            {booking.notes && (
              <div className={styles.infoRow}>
                <span className={styles.infoLabel}>Notes:</span>
                <span className={styles.infoValue}>{booking.notes}</span>
              </div>
            )}
          </div>

          {/* Status History */}
          <BookingStatusHistory bookingId={booking.id} />

          <div className={styles.actions}>
            <button
              className={styles.editButton}
              onClick={onEdit}
            >
              Edit Booking
            </button>
            {booking.status !== 'canceled' && booking.status !== 'completed' && (
              <button
                className={styles.cancelBookingButton}
                onClick={handleCancelBooking}
                disabled={loading}
              >
                {loading ? 'Canceling...' : 'Cancel Booking'}
              </button>
            )}
          </div>
        </div>
      }
    </div>
  );
}
