/**
 * Admin Auth Migration Helper
 *
 * This script updates frontend components to use the new API client
 * for handling authenticated API requests.
 *
 * To use, import this module in _app.js or a context provider
 */

import apiClient from './api-client';

export async function initializeAdminAuth() {
  try {
    // Skip on server-side
    if (typeof window === 'undefined') {
      return;
    }

    const isDevMode = process.env.NODE_ENV !== 'production';
    if (isDevMode) {
      console.log('Initializing admin authentication system...');
    }

    // Clear any existing tokens in localStorage to ensure a clean state
    // We're standardizing on using sessionStorage only for token storage
    if (typeof window !== 'undefined') {
      try {        if (window.localStorage) {
          localStorage.removeItem('oss_auth_token');
          localStorage.removeItem('sb_auth_token');
        }
      } catch (clearError) {
        console.warn('Error clearing localStorage tokens:', clearError);
      }
    }

    // Check if we're already authenticated before proceeding
    let tokenResult;
    try {
      tokenResult = await apiClient.getAuthToken();
    } catch (tokenError) {
      console.error('Error refreshing auth token:', tokenError);
      // Continue with initialization even if token fetch fails
    }

    if (!tokenResult) {
      console.warn('Admin auth initialization: No authentication token available');
      // Don't exit - we'll still patch fetch for when authentication happens later
    }

    // Store original fetch for later restoration if needed
    if (!window._originalFetch) {
      window._originalFetch = window.fetch;
    }

    // Monkey patch fetch for admin API calls to automatically include auth
    window.fetch = async function patchedFetch(url, options = {}) {
      // Handle cross-origin requests for development
      let requestUrl = url;
      const isDev = process.env.NODE_ENV === 'development';
      const allowCrossOrigin = process.env.NEXT_PUBLIC_ALLOW_CROSS_ORIGIN === 'true';

      // If we're in development and cross-origin is allowed, ensure the URL is absolute
      if (isDev && allowCrossOrigin && typeof url === 'string' && url.startsWith('/')) {
        const devUrl = process.env.NEXT_PUBLIC_DEV_URL || window.location.origin;
        requestUrl = `${devUrl}${url}`;
        console.log(`Cross-origin request: ${url} -> ${requestUrl}`);
      }

      // Only intercept admin API and auth-required API requests
      if ((typeof requestUrl === 'string' && (
          requestUrl.includes('/api/admin/') ||
          requestUrl.includes('/api/auth/') ||
          requestUrl.includes('requiresAuth=true')
      ))) {
        // Clone options to avoid modifying the original
        const newOptions = { ...options };

        // Ensure headers object exists
        newOptions.headers = newOptions.headers || {};

        // Get token from sessionStorage directly - this is the most reliable method
        let token = null;
        if (typeof window !== 'undefined' && window.sessionStorage) {
          try {
            const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
            if (cachedToken) {
              try {
                const tokenData = JSON.parse(cachedToken);
                if (tokenData && tokenData.token) {
                  // Use the token even if it's expired - the server will handle token refresh
                  token = tokenData.token;
                  console.log(`Using token from sessionStorage for request: ${url}`);
                }
              } catch (parseError) {
                console.warn(`Error parsing token from sessionStorage:`, parseError);
              }
            }
          } catch (storageError) {
            console.warn(`Error accessing sessionStorage:`, storageError);
          }
        }

        // If no token in sessionStorage, try to get it from the API client
        if (!token) {
          try {
            token = await apiClient.getAuthToken();
            console.log(`Using token from API client for request: ${url}`);
          } catch (tokenError) {
            console.warn(`Error getting token from API client:`, tokenError);
          }
        }

        // Add token to Authorization header if we have one
        if (token) {
          newOptions.headers['Authorization'] = `Bearer ${token}`;
        } else {
          console.warn(`No token available for request: ${url}`);
        }

        // Add credentials to ensure cookies are sent
        newOptions.credentials = 'include';

        // Use the original fetch with our modified options
        // This is more reliable than using the API client which might have its own token handling
        try {
          const response = await window._originalFetch(requestUrl, newOptions);

          // If we get a 401 Unauthorized, try to refresh the token and retry
          if (response.status === 401) {
            console.warn(`Unauthorized response for ${url}, attempting token refresh...`);

            try {
              // Force token refresh
              const newToken = await apiClient.refreshAuthToken();

              if (newToken) {
                console.log(`Token refreshed, retrying request: ${url}`);

                // Update the Authorization header with the new token
                newOptions.headers['Authorization'] = `Bearer ${newToken}`;

                // Retry the request with the new token
                return window._originalFetch(requestUrl, newOptions);
              } else {
                console.error(`Token refresh failed for ${url}`);
                return response; // Return the original 401 response
              }
            } catch (refreshError) {
              console.error(`Error refreshing token:`, refreshError);
              return response; // Return the original 401 response
            }
          }

          return response;
        } catch (fetchError) {
          console.error(`Error fetching ${url}:`, fetchError);
          throw fetchError;
        }
      }

      // Otherwise use original fetch
      return window._originalFetch(url, options);
    };

    if (isDevMode) {
      console.log('Admin authentication system initialized successfully');
    }
    return true;
  } catch (error) {
    console.error('Failed to initialize admin authentication system:', error);
    // Don't throw - allow the application to continue even if auth init fails
    return false;
  }
}

export default {
  initializeAdminAuth,
};
