import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

/**
 * GoogleVerification Component
 * 
 * Fetches Google Search Console verification setting from admin settings
 * and conditionally renders the verification meta tag only on public pages.
 * 
 * Security: Excludes admin pages to prevent Google from accessing/indexing admin areas.
 */
const GoogleVerification = () => {
  const [verificationCode, setVerificationCode] = useState('');
  const router = useRouter();

  // Check if current page is an admin page
  const isAdminPage = router.pathname.startsWith('/admin');

  useEffect(() => {
    // Only fetch verification code for public pages
    if (isAdminPage) {
      return;
    }

    const fetchVerificationCode = async () => {
      try {
        const response = await fetch('/api/admin/settings', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();
          const code = data.settings?.google_search_console_verification;
          
          // Only set if we have a valid verification code
          if (code && code.trim()) {
            setVerificationCode(code.trim());
          }
        }
      } catch (error) {
        // Silently fail - verification is optional
        console.debug('Google verification fetch failed:', error);
      }
    };

    fetchVerificationCode();
  }, [isAdminPage, router.pathname]);

  // Don't render anything on admin pages or if no verification code
  if (isAdminPage || !verificationCode) {
    return null;
  }

  return (
    <Head>
      <meta name="google-site-verification" content={verificationCode} />
    </Head>
  );
};

export default GoogleVerification;
