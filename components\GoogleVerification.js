import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

/**
 * GoogleVerification Component
 *
 * Handles Google Search Console verification through both meta tag and HTML file methods.
 *
 * Meta Tag Method: Fetches verification code from admin settings and renders meta tag
 * HTML File Method: Files are uploaded via admin interface and served directly from public directory
 *
 * Security: Excludes admin pages to prevent Google from accessing/indexing admin areas.
 */
const GoogleVerification = () => {
  const [verificationCode, setVerificationCode] = useState('');
  const router = useRouter();

  // Check if current page is an admin page
  const isAdminPage = router.pathname.startsWith('/admin');

  useEffect(() => {
    // Only fetch verification code for public pages
    if (isAdminPage) {
      return;
    }

    const fetchVerificationCode = async () => {
      try {
        const response = await fetch('/api/admin/settings', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();
          const code = data.settings?.google_search_console_verification;

          // Only set if we have a valid verification code
          if (code && code.trim()) {
            setVerificationCode(code.trim());
          }
        }
      } catch (error) {
        // Silently fail - verification is optional
        console.debug('Google verification fetch failed:', error);
      }
    };

    fetchVerificationCode();
  }, [isAdminPage, router.pathname]);

  // Don't render anything on admin pages
  if (isAdminPage) {
    return null;
  }

  // Only render meta tag if verification code is set
  // HTML file verification is handled automatically by Next.js serving files from public directory
  if (!verificationCode) {
    return null;
  }

  return (
    <Head>
      <meta name="google-site-verification" content={verificationCode} />
    </Head>
  );
};

export default GoogleVerification;
