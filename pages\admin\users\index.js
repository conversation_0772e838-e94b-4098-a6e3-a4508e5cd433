import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import UserList from '@/components/admin/users/UserList'
import styles from '@/styles/admin/UsersPage.module.css'

export default function UsersPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Handle new user button click
  const handleNewUser = () => {
    router.push('/admin/users/new')
  }

  return (
    <ProtectedRoute adminOnly>
      <AdminLayout title="User Management">
        <div className={styles.usersPage}>
          <div className={styles.header}>
            <h2>User Management</h2>
            <div className={styles.actions}>
              <button 
                className={styles.addButton}
                onClick={handleNewUser}
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 5v14M5 12h14"></path>
                </svg>
                Add New User
              </button>
            </div>
          </div>

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          <UserList />
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
