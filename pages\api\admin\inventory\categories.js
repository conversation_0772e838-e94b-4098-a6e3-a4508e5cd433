import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for product category management
 * 
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Authenticate request
  const { authorized, error, user } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getCategories(req, res);
    case 'POST':
      return createCategory(req, res);
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

/**
 * Get product categories with optional filters
 */
async function getCategories(req, res) {
  try {
    // Start building the query
    let query = supabaseAdmin
      .from('product_categories')
      .select('*');

    // Apply parent filter if provided
    if (req.query.parent_id) {
      query = query.eq('parent_id', req.query.parent_id);
    }

    // Execute query
    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    // Return categories
    return res.status(200).json({
      categories: data || [],
      count: count || 0
    });
  } catch (err) {
    console.error('Error fetching categories:', err);
    return res.status(500).json({ error: 'Failed to fetch categories' });
  }
}

/**
 * Create a new product category
 */
async function createCategory(req, res) {
  try {
    const { name, description, parent_id } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }

    // Create category in database
    const { data, error } = await supabaseAdmin
      .from('product_categories')
      .insert([
        {
          name,
          description,
          parent_id
        }
      ])
      .select();

    if (error) {
      throw error;
    }

    // Return created category
    return res.status(201).json({
      success: true,
      category: data[0]
    });
  } catch (err) {
    console.error('Error creating category:', err);
    return res.status(500).json({ error: 'Failed to create category' });
  }
}
