.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--header-height);
  background-color: #000000;
  transition: var(--transition-normal);
  z-index: 1000;
  padding: 0 var(--spacing-lg);
}

.navbar.scrolled {
  background-color: rgba(0, 0, 0, 0.95);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
  height: calc(var(--header-height) - 20px);
}

.navbarContainer {
  max-width: var(--max-width);
  height: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logoContainer {
  display: flex;
  align-items: center;
  height: 100%;
  z-index: 1001;
}

.logo {
  height: 50px;
  transition: var(--transition-normal);
}

.scrolled .logo {
  height: 40px;
}

.desktopNav {
  display: flex;
  height: 100%;
}

.navList {
  display: flex;
  list-style: none;
  height: 100%;
  margin: 0;
  padding: 0;
}

.navItem {
  height: 100%;
  display: flex;
  align-items: center;
  margin: 0 var(--spacing-md);
}

.navLink {
  position: relative;
  color: white;
  font-family: var(--font-primary);
  font-weight: 500;
  font-size: 1rem;
  text-decoration: none;
  padding: 0.5rem 0;
  transition: var(--transition-normal);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.navLinkUnderline {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition-normal);
}

.navLink:hover .navLinkUnderline,
.navLink.active .navLinkUnderline {
  width: 100%;
}

.navLink.active {
  color: var(--primary-color);
}

.bookButton {
  padding: 0.5rem 1.25rem;
  font-size: 0.9rem;
  background: var(--primary-gradient);
  color: white;
  border-radius: var(--border-radius);
  font-weight: 600;
}

/* Mobile menu button */
.mobileMenuButton {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1001;
}

.mobileMenuButton span {
  width: 100%;
  height: 2px;
  background-color: white;
  transition: var(--transition-normal);
}

.mobileMenuButton.open span:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
  background-color: var(--primary-color);
}

.mobileMenuButton.open span:nth-child(2) {
  opacity: 0;
}

.mobileMenuButton.open span:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
  background-color: var(--primary-color);
}

/* Mobile menu */
.mobileMenu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: var(--background-color);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);
  transform: translateX(100%);
  transition: transform 0.4s cubic-bezier(0.77, 0, 0.175, 1);
  overflow-y: auto;
}

.mobileMenu.open {
  transform: translateX(0);
}

.mobileNavList {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
  text-align: center;
}

.mobileNavItem {
  margin: var(--spacing-lg) 0;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
  transition-delay: calc(var(--index) * 0.1s);
}

.mobileMenu.open .mobileNavItem {
  opacity: 1;
  transform: translateY(0);
}

.mobileNavLink {
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition-normal);
}

.mobileNavLink:hover,
.mobileNavLink.active {
  color: var(--primary-color);
}

.mobileBookButton {
  display: inline-block;
  background: var(--primary-gradient);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: var(--border-radius-lg);
  font-family: var(--font-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: var(--spacing-lg);
  transition: var(--transition-bounce);
}

.mobileBookButton:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.mobileMenuFooter {
  margin-top: var(--spacing-3xl);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.socialLinks {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--background-off-white);
  transition: var(--transition-normal);
}

.socialLink:hover {
  transform: translateY(-3px);
  background-color: var(--primary-light);
}

.socialLink img {
  width: 20px;
  height: 20px;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .navItem {
    margin: 0 var(--spacing-sm);
  }

  .navLink {
    font-size: 0.9rem;
  }

  .bookButton {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0 var(--spacing-md);
  }

  .desktopNav,
  .bookButton {
    display: none;
  }

  .mobileMenuButton {
    display: flex;
  }

  .mobileNavItem:nth-child(1) { --index: 1; }
  .mobileNavItem:nth-child(2) { --index: 2; }
  .mobileNavItem:nth-child(3) { --index: 3; }
  .mobileNavItem:nth-child(4) { --index: 4; }
  .mobileNavItem:nth-child(5) { --index: 5; }
  .mobileNavItem:nth-child(6) { --index: 6; }
}
