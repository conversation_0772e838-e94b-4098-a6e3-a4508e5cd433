import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import styles from '@/styles/BookOnline.module.css'
import Layout from '@/components/Layout'
import AnimatedSection from '@/components/AnimatedSection'
import BookingModal from '@/components/BookingModal'

// Updated bookingData
const bookingData = {
  pageTitle: "Book Your Services - OCEANSOULSPARKLES",
  pageDescription: "Book your face painting, airbrush body art, UV painting, hair braiding, and more with OCEANSOULSPARKLES. View our services and book online.",
  hero: {
    title: "Book Your Creative Experience",
    description: "Choose from our range of artistic services to add excitement to any occasion. From face painting and airbrush body art to hair braiding and glitter bars, we have something for everyone!",
    image: "/images/booking-hero.jpg" // Ensure this image exists and is suitable
  },
  intro: {
    title: "Our Creative Services",
    text: "At OCEANSOULSPARKLES, we bring creativity, colour, and sparkle to every event. From enchanting face painting and dazzling body art to vibrant festival braiding and eco-friendly glitter, our services make your moments magical."
  },
  servicesIntro: "Choose from our range of services:",
  howToBook: {
    title: "How to Book Your Services",
    steps: [
      "Select your desired service from the options above.",
      "Click the 'Book Now' button for services that are immediately available.",
      "For services marked 'Request to Book', we'll review your request and confirm availability.",
      "Follow the prompts to provide details about your event or appointment.",
      "For most event bookings, a deposit is required to secure your date.",
      "You'll receive a confirmation email with all the details once your booking is confirmed."
    ]
  },
  paymentMethods: {
    title: "Payment Information",
    text: "We accept various payment methods including credit/debit cards and direct bank transfer. For event bookings such as parties, festivals, and corporate events, a deposit is required to secure your booking, with the remainder payable on the day of service. Individual appointments can be paid for at the time of service."
  },
  cancellationPolicy: {
    title: "Cancellation & Rescheduling",
    text: "We understand that plans can change. If you need to cancel or reschedule, please provide at least 48 hours notice for individual appointments and 7 days notice for events. Deposits for events are non-refundable but may be transferable to another date subject to availability. We appreciate your understanding."
  },
  contactInfo: {
    title: "Questions?",
    text: "If you have any questions about our services or booking, please don't hesitate to contact us!",
    email: "<EMAIL>",
    phone: "" // No phone number provided on the website
  }
};

function ServiceCard({ service, onBookService }) {
  // Apply EXACT working pattern from /services page - ensure all data is primitive strings
  const serviceName = String(service?.name || '');
  const serviceDescription = String(service?.description || '');
  const serviceDuration = String(service?.duration || '');
  const servicePrice = String(service?.price || '');
  const serviceImage = String(service?.image || '');
  const serviceBookingType = String(service?.bookingType || 'Book Now');
  const serviceBookingLink = String(service?.bookingLink || '#');
  const isRequestBooking = serviceBookingType === 'Request to Book';

  const handleBookingClick = (e) => {
    e.preventDefault();
    if (service?.isExternalLink) {
      window.open(serviceBookingLink, '_blank', 'noopener,noreferrer');
    } else {
      onBookService(service);
    }
  };

  return (
    <div className={styles.serviceCard}>
      {serviceImage && (
        <div className={styles.serviceImageContainer}>
          <img
            src={serviceImage || '/images/services/placeholder.jpg'}
            alt={serviceName || 'Service'}
            width="400"
            height="250"
            className={styles.serviceImage}
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = '/images/services/placeholder.jpg';
            }}
          />
        </div>
      )}
      <div className={styles.serviceCardContent}>
        <h3 className={styles.serviceName}>{serviceName}</h3>
        <p className={styles.serviceDescription}>{serviceDescription}</p>
        <div className={styles.serviceDetails}>
          {serviceDuration !== 'N/A' && <p><strong>Duration:</strong> {serviceDuration}</p>}
          <p><strong>Price:</strong> {servicePrice}</p>
        </div>
        <button
          onClick={handleBookingClick}
          className={`${styles.button} ${styles.buttonPrimary} ${isRequestBooking ? styles.buttonSecondary : ''}`}
        >
          {serviceBookingType}
        </button>
      </div>
    </div>
  );
}

export default function BookOnlinePage() {
  const router = useRouter();
  const [selectedService, setSelectedService] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [preSelectedServiceId, setPreSelectedServiceId] = useState(null);

  // Transform API service data to format expected by booking flow
  const transformApiServiceToBookingFormat = (apiService) => {
    if (!apiService || typeof apiService !== 'object') return null;

    // Format pricing from pricing tiers or fallback to basic price
    let priceDisplay = 'Contact for pricing';
    let pricingArray = [];

    if (apiService.pricingTiers && apiService.pricingTiers.length > 0) {
      // Use pricing tiers from database
      pricingArray = apiService.pricingTiers.map(tier => ({
        title: `${tier.name || 'Service'} (${tier.duration || 0} min)`,
        price: `A$${tier.price || 0}`
      }));

      const defaultTier = apiService.pricingTiers.find(tier => tier.is_default) || apiService.pricingTiers[0];
      if (defaultTier && defaultTier.price) {
        priceDisplay = `From A$${defaultTier.price}`;
      }
    } else if (apiService.pricing && Array.isArray(apiService.pricing)) {
      // Use existing pricing format
      pricingArray = apiService.pricing;
      if (pricingArray.length > 0 && pricingArray[0].price) {
        priceDisplay = pricingArray[0].price;
      }
    } else if (apiService.price) {
      // Fallback to basic price
      priceDisplay = `A$${apiService.price}`;
      pricingArray = [{
        title: `Standard (${apiService.duration || 0} min)`,
        price: `A$${apiService.price}`
      }];
    }

    // Format duration from pricing tiers or fallback to service duration
    let durationDisplay = 'Contact for details';
    if (apiService.pricingTiers && apiService.pricingTiers.length > 0) {
      const defaultTier = apiService.pricingTiers.find(tier => tier.is_default) || apiService.pricingTiers[0];
      if (defaultTier && defaultTier.duration) {
        const hours = Math.floor(defaultTier.duration / 60);
        const minutes = defaultTier.duration % 60;
        if (hours > 0 && minutes > 0) {
          durationDisplay = `${hours}h ${minutes}m`;
        } else if (hours > 0) {
          durationDisplay = `${hours} hour${hours > 1 ? 's' : ''}`;
        } else if (minutes > 0) {
          durationDisplay = `${minutes} minutes`;
        }
      }
    }

    return {
      id: String(apiService.id || ''),
      name: String(apiService.title || ''),
      description: String(apiService.description || ''),
      duration: durationDisplay,
      price: priceDisplay,
      image: String(apiService.image || '/images/services/face-paint.jpg'),
      bookingType: 'Request to Book', // All services use request booking for consistency
      bookingLink: '#',
      isExternalLink: false,
      // Include pricing tiers for the booking modal
      pricingTiers: apiService.pricingTiers || []
    };
  };

  // Fetch services from database API
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        console.log('🔄 Fetching services from API for Book Online page...');

        const response = await fetch('/api/public/services');
        if (!response.ok) {
          throw new Error(`Failed to fetch services: ${response.status}`);
        }

        const data = await response.json();
        console.log('✅ Services API response:', data);
        console.log('📊 Services count from API:', data.services?.length || 0);

        if (data.services && Array.isArray(data.services)) {
          // Transform API services to booking format
          const transformedServices = data.services
            .map(transformApiServiceToBookingFormat)
            .filter(Boolean);

          console.log('✅ Transformed services for booking:', transformedServices.length, 'services');
          console.log('🔍 Sample service data:', transformedServices[0]);
          setServices(transformedServices);
        } else {
          console.warn('⚠️ No services found in API response');
          setServices([]);
        }
      } catch (error) {
        console.error('❌ Error fetching services:', error);
        setServices([]);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // Handle pre-selected service from URL parameter
  useEffect(() => {
    if (router.isReady && router.query.service && services.length > 0) {
      const serviceId = safeRender(router.query.service, '');
      setPreSelectedServiceId(serviceId);

      // Find the service and auto-open booking modal
      const foundService = services.find(service => service.id === serviceId);
      if (foundService) {
        setSelectedService(foundService);
        setShowModal(true);
      }
    }
  }, [router.isReady, router.query.service, services]);

  const handleBookService = (service) => {
    // Service data is already transformed and safe for React rendering
    if (service && service.id) {
      setSelectedService(service);
      setShowModal(true);
    } else {
      console.error('Invalid service data provided to handleBookService:', service);
    }
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedService(null);
    // Clear URL parameter when modal is closed
    if (router.query.service) {
      router.replace('/book-online', undefined, { shallow: true });
    }
  };

  return (
    <Layout>
      <Head>
        <title>{bookingData.pageTitle}</title>
        <meta name="description" content={bookingData.pageDescription} />
        <link rel="canonical" href="https://www.oceansoulsparkles.com.au/book-online" />
      </Head>
      <main className={styles.main}>
        <section className={styles.hero} style={{ backgroundImage: bookingData.hero.image ? `linear-gradient(rgba(222, 235, 255, 0.4), rgba(250, 235, 255, 0.4)), url(${bookingData.hero.image})` : 'none' }}>
          <div className={styles.heroContent}>
            <h1 className={styles.title}>{bookingData.hero.title}</h1>
            <p className={styles.description}>{bookingData.hero.description}</p>
          </div>
        </section>

        <section className={styles.introSection}>
          <h2>{bookingData.intro.title}</h2>
          <p>{bookingData.intro.text}</p>
        </section>

        <section className={styles.servicesSection}>
          <h2>{bookingData.servicesIntro}</h2>
          {loading ? (
            <div className={styles.loadingContainer}>
              <p>Loading services...</p>
            </div>
          ) : (
            <div className={styles.servicesGrid}>
              {services.map((service) => {
                // Apply EXACT working pattern from /services page - ensure all data is strings
                return (
                  <div
                    key={String(service.id || Math.random())}
                    className={`${styles.serviceCardWrapper} ${
                      String(preSelectedServiceId || '') === String(service.id || '') ? styles.highlighted : ''
                    }`}
                  >
                    <ServiceCard
                      service={service}
                      onBookService={handleBookService}
                    />
                  </div>
                );
              })}
            </div>
          )}
        </section>

        <section className={styles.howToBookSection}>
          <div className={styles.infoCard}>
            <h3>{bookingData.howToBook.title}</h3>
            <ul>
              {bookingData.howToBook.steps.map((step, index) => (
                <li key={index}>{step}</li>
              ))}
            </ul>
          </div>
        </section>

        <section className={styles.additionalInfoSection}>
          <div className={styles.infoCard}>
            <h3>{bookingData.paymentMethods.title}</h3>
            <p>{bookingData.paymentMethods.text}</p>
          </div>
          <div className={styles.infoCard}>
            <h3>{bookingData.cancellationPolicy.title}</h3>
            <p>{bookingData.cancellationPolicy.text}</p>
          </div>
        </section>

        <section className={styles.policiesSection}>
          <div className={styles.infoCard}>
            <h3>Our Policies</h3>
            <p>Please review our policies before booking or making a purchase:</p>
            <div className={styles.policyLinks}>
              <Link href="/policies#shipping-info" className={styles.policyLink}>
                Shipping Information
              </Link>
              <Link href="/policies#return-policy" className={styles.policyLink}>
                Return & Refund Policy
              </Link>
            </div>
            <p className={styles.policyNote}>
              For event bookings, please note that we require a deposit to secure your date. This deposit is non-refundable but may be transferable to another date subject to availability.
              For product purchases such as UV Liner Palettes, we cannot accept returns on opened or used items due to hygiene concerns.
            </p>
          </div>
        </section>

        <section className={styles.contactSection}>
         <div className={styles.infoCard}>
          <h3>{bookingData.contactInfo.title}</h3>
          <p>{bookingData.contactInfo.text}</p>
          <p>Email: <a href={`mailto:${bookingData.contactInfo.email}`}>{bookingData.contactInfo.email}</a></p>
          {bookingData.contactInfo.phone && <p>Phone: {bookingData.contactInfo.phone}</p>}
          </div>
        </section>
      </main>

      {/* Render the modal at the page level */}
      {showModal && selectedService && (
        <BookingModal
          service={selectedService}
          onClose={handleCloseModal}
          isRequestBooking={selectedService.bookingType === 'Request to Book'}
        />
      )}
    </Layout>
  );
}
