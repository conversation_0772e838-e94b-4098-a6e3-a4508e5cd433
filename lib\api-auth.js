/**
 * API Authentication Middleware for Ocean Soul Sparkles
 *
 * This module provides a centralized authentication system for API routes
 * that works consistently across development and production environments.
 *
 * DEPRECATED: This file is maintained for backward compatibility only.
 * New code should use the withAdminAuth function from lib/admin-auth.js instead.
 */
import { getAdminClient } from '@/lib/supabase';

/**
 * Authenticate an API request and check role permissions
 * Works in both development and production environments
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Object} options - Authentication options
 * @param {boolean} options.adminOnly - Whether to require admin role (defaults to true)
 * @param {boolean} options.staffAllowed - Whether staff role is also allowed (defaults to true)
 * @returns {Object|null} - Authentication result with user and role, or null if unauthorized
 */
export async function authenticateRequest(req, res, options = {}) {
  const { adminOnly = true, staffAllowed = true } = options;

  try {
    console.log('API Auth: Authenticating request');
    console.warn('This authentication method is deprecated. Use withAdminAuth from lib/admin-auth.js instead.');

    // Extract token from Authorization header
    let token = null;
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }

    // If no token in header, try to extract from cookies (for production)
    if (!token && req.cookies) {
      token = req.cookies['sb-access-token'] ||
              req.cookies['sb:token'] ||
              req.cookies['supabase-auth-token'];

      // Some cookies store the token as JSON
      if (token && token.startsWith('{')) {
        try {
          const parsed = JSON.parse(token);
          token = parsed.access_token || parsed.token || null;
        } catch (e) {
          console.error('Failed to parse token from cookie:', e);
        }
      }
    }

    if (!token) {
      console.warn('API Auth: No token found in request');
      return res.status(401).json({ error: 'Unauthorized: Authentication required' });
    }

    // Try to authenticate with token using the admin client
    try {
      const adminClient = getAdminClient();

      // Verify token
      const { data, error } = await adminClient.auth.getUser(token);

      if (error || !data.user) {
        console.error('API Auth: Token validation failed:', error);
        return res.status(401).json({ error: 'Unauthorized: Invalid authentication token' });
      }

      // Get user role
      const { data: roleData, error: roleError } = await adminClient
        .from('user_roles')
        .select('role')
        .eq('id', data.user.id)
        .single();

      if (roleError || !roleData) {
        console.error('API Auth: Role verification failed:', roleError);
        return res.status(401).json({ error: 'Unauthorized: Unable to verify user role' });
      }

      const role = roleData.role;
      const user = data.user;

      // Check role permissions
      if (adminOnly && role !== 'admin') {
        if (staffAllowed && role === 'staff') {
          // Staff is allowed if staffAllowed is true
          console.log('API Auth: Staff access granted');
        } else {
          console.warn(`API Auth: Insufficient permissions for user ${user.id} with role ${role}`);
          return res.status(403).json({ error: 'Forbidden: Insufficient permissions' });
        }
      }

      // Request is authenticated
      console.log(`API Auth: Authenticated user ${user.id} with role ${role}`);
      return { user, role };
    } catch (authError) {
      console.error('API Auth: Authentication error:', authError);
      return res.status(500).json({ error: 'Internal server error during authentication' });
    }
  } catch (err) {
    console.error('API Auth: Unexpected error:', err);
    return res.status(500).json({ error: 'Internal server error during authentication' });
  }
}

/**
 * Extract user ID from request regardless of authentication
 * Useful for public endpoints that need optional user context
 *
 * @param {Object} req - Request object
 * @returns {string|null} - User ID if available, null otherwise
 */
export async function extractUserId(req) {
  try {
    console.warn('extractUserId is deprecated. Use the unified Supabase client instead.');

    // Try to get token from header or cookies
    let token = null;
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    } else if (req.cookies) {
      token = req.cookies['sb-access-token'] || req.cookies['supabase-auth-token'];
    }

    if (!token) {
      return null;
    }

    // Try to get user from token using the admin client
    try {
      const adminClient = getAdminClient();
      const { data, error } = await adminClient.auth.getUser(token);

      if (error || !data.user) {
        return null;
      }

      return data.user.id;
    } catch (authError) {
      console.error('Error verifying token:', authError);
      return null;
    }
  } catch (err) {
    console.error('Error extracting user ID:', err);
    return null;
  }
}

// Export a higher-order function to create authenticated API handlers
export function withAuth(handler, options = {}) {
  console.warn('withAuth is deprecated. Use withAdminAuth from lib/admin-auth.js instead.');

  return async (req, res) => {
    const authResult = await authenticateRequest(req, res, options);
    if (!authResult) {
      // Authentication middleware already sent response
      return;
    }

    // Set auth context on request object
    req.auth = authResult;

    // Call the original handler
    return handler(req, res);
  };
}
