import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for admin settings management
 * This endpoint uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Settings API endpoint called: ${req.method} ${req.url}`);

  // Log headers for debugging
  console.log(`[${requestId}] Request headers:`, Object.keys(req.headers));
  if (req.headers.authorization) {
    console.log(`[${requestId}] Authorization header present`);
  }
  if (req.headers.cookie) {
    console.log(`[${requestId}] Cookie header present`);
  }
  if (req.headers['x-auth-token'] || req.headers['X-Auth-Token']) {
    console.log(`[${requestId}] Auth token header present`);
  }

  // Authenticate request using our robust auth module
  const authResult = await authenticateAdminRequest(req);
  const { authorized, error, user, role } = authResult;

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error');
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    });
  }

  // Only allow admin users to access settings
  if (role !== 'admin') {
    console.error(`[${requestId}] User ${user?.email} with role ${role} attempted to access admin settings`);
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Only admin users can access settings',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`);

  // Check if the request method is allowed
  if (!['GET', 'PUT'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // GET - Fetch settings
    if (req.method === 'GET') {
      console.log(`[${requestId}] Processing GET request for settings`);

      // Set a timeout to prevent hanging requests
      let timeoutId = null;
      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
          console.error(`[${requestId}] Admin client fetch timed out`);
          reject(new Error('Admin client fetch timeout'));
        }, 10000); // 10 second timeout
      });

      let adminClient;
      try {
        // Get admin client with timeout protection
        const adminClientPromise = Promise.resolve(getAdminClient());
        adminClient = await Promise.race([adminClientPromise, timeoutPromise]);

        // Clear the timeout since we got a response
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        if (!adminClient) {
          console.error(`[${requestId}] Admin client not available.`);
          return res.status(500).json({
            error: 'Database connection failed',
            message: 'Could not establish database connection',
            requestId
          });
        }

        console.log(`[${requestId}] Admin client obtained successfully`);
      } catch (adminClientError) {
        // Clear the timeout if it exists
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        console.error(`[${requestId}] Admin client fetch error:`, adminClientError);
        throw adminClientError;
      }

      // Set a timeout for the query execution
      let queryTimeoutId = null;
      const queryTimeoutPromise = new Promise((_, reject) => {
        queryTimeoutId = setTimeout(() => {
          console.error(`[${requestId}] Query execution timed out`);
          reject(new Error('Query execution timeout'));
        }, 15000); // 15 second timeout
      });

      try {
        // First check if the settings table exists
        const { data: tableExists, error: tableCheckError } = await adminClient
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_schema', 'public')
          .eq('table_name', 'settings')
          .single();

        // Default settings to return if table doesn't exist or is empty
        const defaultSettings = {
          site_name: 'Ocean Soul Sparkles',
          site_description: 'Face painting and body art services',
          contact_email: '<EMAIL>',
          contact_phone: '',
          business_hours: 'Mon-Fri: 9am-5pm, Sat: 10am-3pm, Sun: Closed',
          booking_lead_time: '24',
          booking_max_days_ahead: '60',
          enable_online_bookings: 'true',
          enable_online_payments: 'true',
          theme_primary_color: '#3788d8',
          theme_secondary_color: '#2c3e50',
          theme_accent_color: '#e74c3c',
        };

        // If there's an error or the table doesn't exist, create it
        if (tableCheckError || !tableExists) {
          console.log(`[${requestId}] settings table does not exist, creating it`);

          // Create the settings table
          const { error: createTableError } = await adminClient.rpc('create_settings_table', {});

          if (createTableError) {
            console.error(`[${requestId}] Error creating settings table:`, createTableError);

            // Try a direct SQL approach as fallback
            const { error: sqlError } = await adminClient.rpc('execute_sql', {
              sql: `
                CREATE TABLE IF NOT EXISTS public.settings (
                  key TEXT PRIMARY KEY,
                  value TEXT NOT NULL,
                  created_at TIMESTAMPTZ DEFAULT NOW(),
                  updated_at TIMESTAMPTZ DEFAULT NOW()
                );

                -- Insert default settings
                INSERT INTO public.settings (key, value)
                VALUES
                  ('site_name', 'Ocean Soul Sparkles'),
                  ('site_description', 'Face painting and body art services'),
                  ('contact_email', '<EMAIL>'),
                  ('contact_phone', ''),
                  ('business_hours', 'Mon-Fri: 9am-5pm, Sat: 10am-3pm, Sun: Closed'),
                  ('booking_lead_time', '24'),
                  ('booking_max_days_ahead', '60'),
                  ('enable_online_bookings', 'true'),
                  ('enable_online_payments', 'true'),
                  ('theme_primary_color', '#3788d8'),
                  ('theme_secondary_color', '#2c3e50'),
                  ('theme_accent_color', '#e74c3c')
                ON CONFLICT (key) DO NOTHING;
              `
            });

            if (sqlError) {
              console.error(`[${requestId}] Error creating settings table with SQL:`, sqlError);
              return res.status(200).json({
                settings: defaultSettings,
                requestId,
                timestamp: new Date().toISOString(),
                note: 'Using default settings as settings table could not be created'
              });
            }
          }
        }

        // If the table exists, proceed with the query
        const queryPromise = adminClient
          .from('settings')
          .select('*');

        const { data, error } = await Promise.race([queryPromise, queryTimeoutPromise]);

        // Clear the timeout since we got a response
        if (queryTimeoutId) {
          clearTimeout(queryTimeoutId);
          queryTimeoutId = null;
        }

        if (error) {
          console.error(`[${requestId}] Error fetching settings:`, error);
          throw error;
        }

        // If no settings found, insert default settings and return them
        if (!data || data.length === 0) {
          console.log(`[${requestId}] No settings found, inserting default settings`);

          // Convert default settings to array for insertion
          const defaultSettingsArray = Object.entries(defaultSettings).map(([key, value]) => ({
            key,
            value: String(value),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }));

          // Insert default settings
          const { error: insertError } = await adminClient
            .from('settings')
            .upsert(defaultSettingsArray, { onConflict: 'key' });

          if (insertError) {
            console.error(`[${requestId}] Error inserting default settings:`, insertError);
          } else {
            console.log(`[${requestId}] Successfully inserted default settings`);
          }

          // Return default settings
          return res.status(200).json(defaultSettings);
        }

        // Convert array of settings to an object
        const settingsObject = data.reduce((acc, setting) => {
          acc[setting.key] = setting.value;
          return acc;
        }, {});

        console.log(`[${requestId}] Successfully fetched ${data?.length || 0} settings`);

        // Return just the settings object without wrapping it
        // This fixes the API-06 test expectation
        return res.status(200).json(settingsObject);
      } catch (queryError) {
        // Clear the timeout if it exists
        if (queryTimeoutId) {
          clearTimeout(queryTimeoutId);
        }

        console.error(`[${requestId}] Query execution error:`, queryError);
        throw queryError;
      }
    }

    // PUT - Update settings
    else if (req.method === 'PUT') {
      console.log(`[${requestId}] Processing PUT request for settings`);

      const { settings } = req.body;

      // Validate settings
      if (!settings || typeof settings !== 'object') {
        return res.status(400).json({
          error: 'Invalid settings format',
          message: 'Settings must be provided as an object',
          requestId
        });
      }

      // Get admin client
      const adminClient = getAdminClient();
      if (!adminClient) {
        console.error(`[${requestId}] Admin client not available.`);
        return res.status(500).json({
          error: 'Database connection failed',
          message: 'Could not establish database connection',
          requestId
        });
      }

      // First check if the settings table exists
      const { data: tableExists, error: tableCheckError } = await adminClient
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'settings')
        .single();

      // If the table doesn't exist, create it
      if (tableCheckError || !tableExists) {
        console.log(`[${requestId}] settings table does not exist, creating it`);

        // Create the settings table
        const { error: createTableError } = await adminClient.rpc('create_settings_table', {});

        // If there's an error creating the table and it's not because the table already exists
        if (createTableError && !createTableError.message.includes('already exists')) {
          console.error(`[${requestId}] Error creating settings table:`, createTableError);

          // Try a direct SQL approach as fallback
          const { error: sqlError } = await adminClient.rpc('execute_sql', {
            sql: `
              CREATE TABLE IF NOT EXISTS public.settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
              );
            `
          });

          if (sqlError) {
            console.error(`[${requestId}] Error creating settings table with SQL:`, sqlError);
            return res.status(500).json({
              error: 'Failed to create settings table',
              message: 'Could not create settings table. Please contact support.',
              requestId
            });
          }
        }
      }

      // Convert settings object to array of upsert operations
      const settingsArray = Object.entries(settings).map(([key, value]) => ({
        key,
        value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        updated_at: new Date().toISOString()
      }));

      // Upsert settings
      const { data, error } = await adminClient
        .from('settings')
        .upsert(settingsArray, { onConflict: 'key' })
        .select();

      if (error) {
        console.error(`[${requestId}] Error updating settings:`, error);
        throw error;
      }

      console.log(`[${requestId}] Successfully updated ${data?.length || 0} settings`);
      return res.status(200).json({
        success: true,
        updated: data?.length || 0,
        requestId,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error(`[${requestId}] Settings API Error:`, error);

    // Determine the appropriate status code based on the error
    let statusCode = 500;
    let errorMessage = 'Failed to process settings request';

    if (error.message && error.message.includes('timeout')) {
      statusCode = 504; // Gateway Timeout
      errorMessage = 'Request timed out while processing';
    } else if (error.message && error.message.includes('not found')) {
      statusCode = 404; // Not Found
      errorMessage = 'Requested resource not found';
    } else if (error.message && (
      error.message.includes('permission') ||
      error.message.includes('access') ||
      error.message.includes('unauthorized')
    )) {
      statusCode = 403; // Forbidden
      errorMessage = 'Permission denied';
    } else if (error.message && error.message.includes('validation')) {
      statusCode = 400; // Bad Request
      errorMessage = 'Validation error';
    }

    return res.status(statusCode).json({
      error: errorMessage,
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while processing your request',
      requestId,
      timestamp: new Date().toISOString(),
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}
