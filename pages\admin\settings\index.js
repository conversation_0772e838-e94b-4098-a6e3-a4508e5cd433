import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import SettingsForm from '@/components/admin/SettingsForm';
import styles from '@/styles/admin/SettingsPage.module.css';

export default function SettingsPage() {
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch settings
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Get auth token from standardized location
        let authToken = null;
        if (typeof window !== 'undefined' && window.sessionStorage) {
          try {
            const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
            if (cachedToken) {
              const tokenData = JSON.parse(cachedToken);
              if (tokenData && tokenData.token) {
                authToken = tokenData.token;
              }
            }
          } catch (tokenError) {
            console.error('Error getting auth token:', tokenError);
          }
        }
        
        const response = await fetch('/api/admin/settings', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            // Add auth token if available
            ...(authToken ? {
              'Authorization': `Bearer ${authToken}`,
              'X-Auth-Token': authToken
            } : {})
          },
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch settings: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        setSettings(data.settings || {});
      } catch (error) {
        console.error('Error fetching settings:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [refreshKey]);

  // Handle settings update
  const handleUpdateSettings = async (updatedSettings) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      // Get auth token from standardized location
      let authToken = null;
      if (typeof window !== 'undefined' && window.sessionStorage) {
        try {
          const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
          if (cachedToken) {
            const tokenData = JSON.parse(cachedToken);
            if (tokenData && tokenData.token) {
              authToken = tokenData.token;
            }
          }
        } catch (tokenError) {
          console.error('Error getting auth token:', tokenError);
        }
      }
      
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          // Add auth token if available
          ...(authToken ? {
            'Authorization': `Bearer ${authToken}`,
            'X-Auth-Token': authToken
          } : {})
        },
        credentials: 'include',
        body: JSON.stringify({ settings: updatedSettings })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to update settings: ${response.status}`);
      }
      
      setSuccess('Settings updated successfully');
      setRefreshKey(prev => prev + 1); // Refresh settings
    } catch (error) {
      console.error('Error updating settings:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProtectedRoute adminOnly>
      <AdminLayout title="Settings">
        <div className={styles.settingsPage}>
          <div className={styles.header}>
            <h1>Application Settings</h1>
          </div>
          
          {error && (
            <div className={styles.errorBox}>
              <p>Error: {error}</p>
              <button onClick={() => setRefreshKey(prev => prev + 1)}>Try Again</button>
            </div>
          )}
          
          {success && (
            <div className={styles.successBox}>
              <p>{success}</p>
            </div>
          )}
          
          {loading && !error ? (
            <div className={styles.loading}>Loading settings...</div>
          ) : (
            <SettingsForm 
              settings={settings} 
              onSave={handleUpdateSettings} 
              loading={loading}
            />
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
