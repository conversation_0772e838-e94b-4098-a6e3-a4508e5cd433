import supabase, { getCurrentUserFromRequest } from '@/lib/supabase'
import { sendOneSignalEmail, sendOneSignalPush } from '@/lib/notifications'

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id } = req.query
  const { test_mode = false, customer_ids = [] } = req.body

  try {
    // Use Supabase client directly
    const client = supabase;

    // Get automation details
    const { data: automation, error: automationError } = await client
      .from('marketing_automations')
      .select(`
        *,
        segment:segment_id (id, name)
      `)
      .eq('id', id)
      .single()

    if (automationError) {
      throw new Error(`Error fetching automation: ${automationError.message}`)
    }

    if (!automation) {
      return res.status(404).json({ error: 'Automation not found' })
    }

    // Check if automation is active
    if (!automation.is_active && !test_mode) {
      return res.status(400).json({ error: 'Cannot trigger an inactive automation' })
    }

    // Get customers to send to
    let customers = []

    if (test_mode) {
      if (customer_ids.length > 0) {
        // Use provided customer IDs for testing
        const { data: testCustomers, error: testCustomersError } = await supabase
          .from('customers')
          .select('id, name, email, phone')
          .in('id', customer_ids)
          .eq('marketing_consent', true)

        if (testCustomersError) {
          throw new Error(`Error fetching test customers: ${testCustomersError.message}`)
        }

        customers = testCustomers || []
      } else {
        // Get a few customers for testing
        const { data: testCustomers, error: testCustomersError } = await supabase
          .from('customers')
          .select('id, name, email, phone')
          .eq('marketing_consent', true)
          .limit(3)

        if (testCustomersError) {
          throw new Error(`Error fetching test customers: ${testCustomersError.message}`)
        }

        customers = testCustomers || []
      }
    } else if (automation.segment_id) {
      // Get segment preview
      const segmentResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SITE_URL}/api/marketing/segments/${automation.segment_id}?action=preview`,
        { method: 'POST' }
      )

      if (!segmentResponse.ok) {
        const errorData = await segmentResponse.json()
        throw new Error(`Error previewing segment: ${errorData.error}`)
      }

      const segmentData = await segmentResponse.json()
      customers = segmentData.customers || []

      // Filter customers by marketing consent
      customers = customers.filter(customer => customer.marketing_consent)
    } else {
      return res.status(400).json({ error: 'No segment defined for this automation' })
    }

    if (customers.length === 0) {
      return res.status(400).json({
        error: 'No customers with marketing consent found',
        test_mode: test_mode
      })
    }

    // Send messages based on message type
    let sentCount = 0
    let failedCount = 0

    for (const customer of customers) {
      try {
        // Replace personalization tokens in subject and content
        const personalizedSubject = personalizeText(automation.subject, customer)
        const personalizedContent = personalizeText(automation.content, customer)

        if (automation.message_type === 'email') {
          // Send email via OneSignal
          if (!customer.email) continue

          await sendOneSignalEmail({
            email: customer.email,
            subject: personalizedSubject,
            message: personalizedContent,
            htmlBody: personalizedContent,
            data: {
              type: 'marketing_automation',
              automation_id: automation.id
            }
          })
        } else if (automation.message_type === 'sms') {
          // Send SMS
          if (!customer.phone) continue

          // In a real implementation, this would use an SMS service
          // For now, we'll just log it
          console.log(`SMS would be sent to ${customer.phone}:`, personalizedContent)
        } else if (automation.message_type === 'push') {
          // Send push notification
          await sendOneSignalPush({
            userIds: [customer.id],
            title: personalizedSubject || 'New notification',
            message: personalizedContent,
            data: {
              type: 'marketing_automation',
              automation_id: automation.id
            }
          })
        }

        // Log successful execution
        if (!test_mode) {
          await supabase
            .from('automation_logs')
            .insert([
              {
                automation_id: automation.id,
                trigger_event: 'manual',
                customer_id: customer.id,
                status: 'success'
              }
            ])
        }

        sentCount++
      } catch (error) {
        console.error(`Error sending message to customer ${customer.id}:`, error)

        // Log failed execution
        if (!test_mode) {
          await supabase
            .from('automation_logs')
            .insert([
              {
                automation_id: automation.id,
                trigger_event: 'manual',
                customer_id: customer.id,
                status: 'failed',
                message: error.message
              }
            ])
        }

        failedCount++
      }
    }

    return res.status(200).json({
      success: true,
      sent: sentCount,
      failed: failedCount,
      total_customers: customers.length,
      test_mode: test_mode
    })
  } catch (error) {
    console.error('Error triggering automation:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Helper function to replace personalization tokens in text
function personalizeText(text, customer) {
  if (!text) return ''

  return text
    .replace(/\{name\}/g, customer.name || 'Customer')
    .replace(/\{first_name\}/g, customer.name ? customer.name.split(' ')[0] : 'Customer')
    .replace(/\{email\}/g, customer.email || '')
    .replace(/\{phone\}/g, customer.phone || '')
    .replace(/\{city\}/g, customer.city || '')
    .replace(/\{state\}/g, customer.state || '')
}
