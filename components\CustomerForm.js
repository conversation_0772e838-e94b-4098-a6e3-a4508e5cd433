// Customer registration and data collection form
import { useState, useEffect } from 'react';
import { useCustomer } from '@/contexts/CustomerContext';
import { useAuth } from '@/contexts/AuthContext';
import { createGuestCustomer, registerCustomer, signInCustomer, updateCustomerProfile } from '@/lib/auth';
import styles from '@/styles/CustomerForm.module.css';

/**
 * Customer registration and data collection form
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isCheckout - Whether this form is being used in checkout flow
 * @param {boolean} props.isEditing - Whether this form is for editing an existing profile
 * @param {Function} props.onComplete - Callback function when form is submitted and completed
 * @returns {JSX.Element}
 */
const CustomerForm = ({ 
  isCheckout = false, 
  isEditing = false,
  onComplete = () => {}
}) => {
  const { customer, saveGuestCustomer, updateCustomer } = useCustomer();
  const { isAuthenticated } = useAuth();
  
  // Form mode can be: 'guest', 'register', 'login' or 'edit'
  const [mode, setMode] = useState(isEditing ? 'edit' : (isCheckout ? 'guest' : 'register'));
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'Australia',
    marketingConsent: false
  });
  
  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [submitError, setSubmitError] = useState('');
  const [submitSuccess, setSubmitSuccess] = useState('');
  
  // Load existing customer data if available
  useEffect(() => {
    if (customer && (isEditing || isCheckout)) {
      setFormData({
        name: customer.name || '',
        email: customer.email || '',
        phone: customer.phone || '',
        password: '',
        confirmPassword: '',
        address: customer.address || '',
        city: customer.city || '',
        state: customer.state || '',
        postal_code: customer.postal_code || '',
        country: customer.country || 'Australia',
        marketingConsent: customer.marketing_consent || false
      });
    }
  }, [customer, isEditing, isCheckout]);
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear any error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };
  
  // Validate the form data
  const validateForm = () => {
    const newErrors = {};
    
    // Validate required fields for all modes
    if (!formData.name) newErrors.name = 'Name is required';
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!formData.phone) newErrors.phone = 'Phone number is required';
    
    // Password validation for register mode
    if (mode === 'register') {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters';
      }
      
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }
    
    // Password validation for login mode
    if (mode === 'login' && !formData.password) {
      newErrors.password = 'Password is required';
    }
    
    // Address validation for checkout
    if (isCheckout) {
      if (!formData.address) newErrors.address = 'Address is required';
      if (!formData.city) newErrors.city = 'City is required';
      if (!formData.state) newErrors.state = 'State is required';
      if (!formData.postal_code) newErrors.postal_code = 'Postal code is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Clear previous messages
    setSubmitError('');
    setSubmitSuccess('');
    
    // Validate form
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    try {
      // Handle different form modes
      switch (mode) {
        case 'guest': {
          // Create guest customer without authentication
          const { data, error } = await createGuestCustomer({
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            address: formData.address,
            city: formData.city,
            state: formData.state,
            postal_code: formData.postal_code,
            country: formData.country,
            marketingConsent: formData.marketingConsent
          });
          
          if (error) throw error;
          
          // Save customer data to context
          saveGuestCustomer(data.customer);
          
          setSubmitSuccess('Your information has been saved');
          onComplete(data.customer);
          break;
        }
        
        case 'register': {
          // Register new customer with authentication
          const { data, error } = await registerCustomer({
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            marketingConsent: formData.marketingConsent
          }, formData.password);
          
          if (error) throw error;
          
          setSubmitSuccess('Registration successful! You are now logged in.');
          onComplete(data.user);
          break;
        }
        
        case 'login': {
          // Sign in existing customer
          const { data, error } = await signInCustomer(formData.email, formData.password);
          
          if (error) throw error;
          
          setSubmitSuccess('Login successful!');
          onComplete(data.customer);
          break;
        }
        
        case 'edit': {
          // Update existing customer profile
          const { data, error } = await updateCustomer({
            name: formData.name,
            phone: formData.phone,
            address: formData.address,
            city: formData.city,
            state: formData.state,
            postal_code: formData.postal_code,
            country: formData.country,
            marketingConsent: formData.marketingConsent
          });
          
          if (error) throw error;
          
          setSubmitSuccess('Profile updated successfully');
          onComplete(data);
          break;
        }
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitError(error.message || 'An error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Switch between login and register modes
  const toggleMode = () => {
    if (mode === 'register') {
      setMode('login');
    } else if (mode === 'login') {
      setMode('register');
    } else if (mode === 'guest' && !isCheckout) {
      setMode('register');
    }
  };
  
  return (
    <div className={styles.formContainer}>
      {!isEditing && isCheckout && !isAuthenticated && (
        <div className={styles.modeToggle}>
          <button 
            type="button" 
            className={`${styles.modeButton} ${mode === 'guest' ? styles.activeMode : ''}`}
            onClick={() => setMode('guest')}
          >
            Guest Checkout
          </button>
          <button 
            type="button" 
            className={`${styles.modeButton} ${mode === 'login' ? styles.activeMode : ''}`}
            onClick={() => setMode('login')}
          >
            Sign In
          </button>
          <button 
            type="button" 
            className={`${styles.modeButton} ${mode === 'register' ? styles.activeMode : ''}`}
            onClick={() => setMode('register')}
          >
            Create Account
          </button>
        </div>
      )}
      
      {!isEditing && !isCheckout && !isAuthenticated && (
        <div className={styles.modeToggle}>
          <button 
            type="button" 
            className={`${styles.modeButton} ${mode === 'login' ? styles.activeMode : ''}`}
            onClick={() => setMode('login')}
          >
            Sign In
          </button>
          <button 
            type="button" 
            className={`${styles.modeButton} ${mode === 'register' ? styles.activeMode : ''}`}
            onClick={() => setMode('register')}
          >
            Create Account
          </button>
        </div>
      )}
      
      <form className={styles.form} onSubmit={handleSubmit}>
        {submitError && <div className={styles.errorMessage}>{submitError}</div>}
        {submitSuccess && <div className={styles.successMessage}>{submitSuccess}</div>}
        
        <div className={styles.formSection}>
          <h2>{mode === 'edit' ? 'Your Information' : 'Contact Information'}</h2>
          
          <div className={styles.formGroup}>
            <label htmlFor="name">Full Name <span className={styles.required}>*</span></label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={errors.name ? styles.inputError : ''}
              disabled={isSubmitting}
              required
            />
            {errors.name && <span className={styles.errorText}>{errors.name}</span>}
          </div>
          
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email <span className={styles.required}>*</span></label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={errors.email ? styles.inputError : ''}
                disabled={isSubmitting || (isEditing && isAuthenticated)}
                required
              />
              {errors.email && <span className={styles.errorText}>{errors.email}</span>}
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="phone">Phone <span className={styles.required}>*</span></label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={errors.phone ? styles.inputError : ''}
                disabled={isSubmitting}
                required
              />
              {errors.phone && <span className={styles.errorText}>{errors.phone}</span>}
            </div>
          </div>
          
          {(mode === 'register' || mode === 'login') && (
            <div className={mode === 'login' ? styles.formGroup : styles.formRow}>
              <div className={styles.formGroup}>
                <label htmlFor="password">Password <span className={styles.required}>*</span></label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={errors.password ? styles.inputError : ''}
                  disabled={isSubmitting}
                  required
                />
                {errors.password && <span className={styles.errorText}>{errors.password}</span>}
              </div>
              
              {mode === 'register' && (
                <div className={styles.formGroup}>
                  <label htmlFor="confirmPassword">Confirm Password <span className={styles.required}>*</span></label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={errors.confirmPassword ? styles.inputError : ''}
                    disabled={isSubmitting}
                    required
                  />
                  {errors.confirmPassword && <span className={styles.errorText}>{errors.confirmPassword}</span>}
                </div>
              )}
            </div>
          )}
        </div>
        
        {(isCheckout || mode === 'edit' || isEditing) && (
          <div className={styles.formSection}>
            <h2>Address Information {!isCheckout && <span className={styles.optional}>(Optional)</span>}</h2>
            
            <div className={styles.formGroup}>
              <label htmlFor="address">
                Address {isCheckout && <span className={styles.required}>*</span>}
              </label>
              <input
                type="text"
                id="address"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                className={errors.address ? styles.inputError : ''}
                disabled={isSubmitting}
                required={isCheckout}
              />
              {errors.address && <span className={styles.errorText}>{errors.address}</span>}
            </div>
            
            <div className={styles.formRow}>
              <div className={styles.formGroup}>
                <label htmlFor="city">
                  City {isCheckout && <span className={styles.required}>*</span>}
                </label>
                <input
                  type="text"
                  id="city"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className={errors.city ? styles.inputError : ''}
                  disabled={isSubmitting}
                  required={isCheckout}
                />
                {errors.city && <span className={styles.errorText}>{errors.city}</span>}
              </div>
              
              <div className={styles.formGroup}>
                <label htmlFor="state">
                  State {isCheckout && <span className={styles.required}>*</span>}
                </label>
                <select
                  id="state"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  className={errors.state ? styles.inputError : ''}
                  disabled={isSubmitting}
                  required={isCheckout}
                >
                  <option value="">Select State</option>
                  <option value="ACT">Australian Capital Territory</option>
                  <option value="NSW">New South Wales</option>
                  <option value="NT">Northern Territory</option>
                  <option value="QLD">Queensland</option>
                  <option value="SA">South Australia</option>
                  <option value="TAS">Tasmania</option>
                  <option value="VIC">Victoria</option>
                  <option value="WA">Western Australia</option>
                </select>
                {errors.state && <span className={styles.errorText}>{errors.state}</span>}
              </div>
            </div>
            
            <div className={styles.formRow}>
              <div className={styles.formGroup}>
                <label htmlFor="postal_code">
                  Postal Code {isCheckout && <span className={styles.required}>*</span>}
                </label>
                <input
                  type="text"
                  id="postal_code"
                  name="postal_code"
                  value={formData.postal_code}
                  onChange={handleInputChange}
                  className={errors.postal_code ? styles.inputError : ''}
                  disabled={isSubmitting}
                  required={isCheckout}
                />
                {errors.postal_code && <span className={styles.errorText}>{errors.postal_code}</span>}
              </div>
              
              <div className={styles.formGroup}>
                <label htmlFor="country">Country</label>
                <input
                  type="text"
                  id="country"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  disabled={true}
                />
              </div>
            </div>
          </div>
        )}
        
        <div className={styles.formSection}>
          <div className={styles.checkboxGroup}>
            <input
              type="checkbox"
              id="marketingConsent"
              name="marketingConsent"
              checked={formData.marketingConsent}
              onChange={handleInputChange}
              disabled={isSubmitting}
            />
            <label htmlFor="marketingConsent">
              I'd like to receive news, promotions, and updates from OceanSoulSparkles
            </label>
          </div>
          
          <div className={styles.privacyConsent}>
            By providing your details, you agree to our <a href="/policies" target="_blank" rel="noopener noreferrer">Privacy Policy</a>.
          </div>
        </div>
        
        <div className={styles.formActions}>
          {mode === 'login' && (
            <button
              type="button"
              className={styles.forgotPasswordButton}
              disabled={isSubmitting}
              onClick={() => alert('Password reset functionality would be implemented here')}
            >
              Forgot Password?
            </button>
          )}
          
          <button
            type="submit"
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Processing...' : getButtonText()}
          </button>
          
          {mode !== 'edit' && !isEditing && !isCheckout && (
            <button
              type="button"
              className={styles.toggleModeButton}
              onClick={toggleMode}
              disabled={isSubmitting}
            >
              {mode === 'login' ? 'Need an account? Register' : 'Already have an account? Sign In'}
            </button>
          )}
        </div>
      </form>
    </div>
  );
  
  // Helper function to get the button text based on the current mode
  function getButtonText() {
    switch (mode) {
      case 'guest':
        return isCheckout ? 'Continue to Shipping' : 'Save Information';
      case 'register':
        return 'Create Account';
      case 'login':
        return 'Sign In';
      case 'edit':
        return 'Update Profile';
      default:
        return 'Submit';
    }
  }
};

export default CustomerForm;
