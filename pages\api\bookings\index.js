import supabase, { getAdminClient } from '@/lib/supabase';
import { sendBookingNotification } from '@/lib/notifications';

/**
 * API endpoint for handling booking submissions from the public website
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Only allow POST requests for creating bookings
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      name,
      email,
      phone,
      date,
      time,
      location,
      message,
      service,
      option
    } = req.body;

    // Validate required fields
    if (!name || !email || !phone || !date || !time || !location || !service || !option) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Use Supabase client directly
    const client = supabase;

    // Check if customer already exists
    const { data: existingCustomer, error: customerCheckError } = await client
      .from('customers')
      .select('id')
      .eq('email', email)
      .single();

    if (customerCheckError && customerCheckError.code !== 'PGRST116') {
      throw customerCheckError;
    }

    let customerId;

    // If customer doesn't exist, create a new one
    if (!existingCustomer) {
      const { data: newCustomer, error: createCustomerError } = await client
        .from('customers')
        .insert([{
          name,
          email,
          phone,
          marketing_consent: true // Default to true for public bookings
        }])
        .select();

      if (createCustomerError) {
        throw createCustomerError;
      }

      customerId = newCustomer[0].id;
    } else {
      customerId = existingCustomer.id;
    }

    // Log the service object for debugging
    console.log('Service object from booking form:', service);

    let serviceId;
    let serviceData;

    // First try to find the service by ID if it exists
    if (service.id) {
      // Try with exact ID match
      const { data: serviceById, error: serviceByIdError } = await client
        .from('services')
        .select('id, name, duration')
        .eq('id', service.id)
        .single();

      if (!serviceByIdError && serviceById) {
        serviceData = [serviceById];
        console.log('Found service by ID:', serviceById);
      } else {
        // If the ID is not a UUID but a string identifier (like 'airbrush-face-body')
        // Try to find by matching the ID as a substring of the name
        if (!service.id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          const searchId = service.id.replace(/-/g, ' ');
          console.log('ID is not a UUID, searching by ID as name part:', searchId);

          const { data: serviceByIdAsName, error: serviceByIdAsNameError } = await client
            .from('services')
            .select('id, name, duration')
            .ilike('name', `%${searchId}%`)
            .limit(1);

          if (!serviceByIdAsNameError && serviceByIdAsName && serviceByIdAsName.length > 0) {
            serviceData = serviceByIdAsName;
            console.log('Found service by ID as name part:', serviceByIdAsName[0]);
          }
        }
      }
    }

    // If not found by ID, try to find by name with improved matching
    if (!serviceData) {
      // Clean the service name by removing emoji prefixes and standardizing
      const cleanServiceName = service.name.replace(/^🎨 |^✨ |^💇 |^🎭 |^🚗 |^🅿️ |^📸 /, '').trim();
      console.log('Searching for service by clean name:', cleanServiceName);

      // Try exact match first
      const { data: exactMatch, error: exactMatchError } = await client
        .from('services')
        .select('id, name, duration')
        .ilike('name', cleanServiceName)
        .limit(1);

      if (!exactMatchError && exactMatch && exactMatch.length > 0) {
        serviceData = exactMatch;
        console.log('Found service by exact name match:', exactMatch[0]);
      } else {
        // Try partial match if exact match fails
        const { data: serviceByName, error: serviceByNameError } = await client
          .from('services')
          .select('id, name, duration')
          .or(`name.ilike.%${cleanServiceName}%,name.ilike.%${service.name}%`)
          .limit(1);

        if (!serviceByNameError && serviceByName && serviceByName.length > 0) {
          serviceData = serviceByName;
          console.log('Found service by partial name match:', serviceByName[0]);
        }
      }
    }

    // If service still not found, create it
    if (!serviceData || serviceData.length === 0) {
      try {
        // Clean the service name by removing emoji prefixes
        const cleanServiceName = service.name.replace(/^🎨 |^✨ |^💇 |^🎭 |^🚗 |^🅿️ |^📸 /, '').trim();
        console.log('Service not found, creating new service with clean name:', cleanServiceName);

        // Validate required fields before creating
        if (!cleanServiceName) {
          throw new Error('Service name is required');
        }

        if (!option || !option.hours || !option.price) {
          throw new Error('Service duration and price are required');
        }

        // Get admin client for service creation
        const adminClient = getAdminClient();
        if (!adminClient) {
          throw new Error('Failed to initialize admin client for service creation');
        }

        // If service doesn't exist in the database, create it
        const { data: newService, error: createServiceError } = await adminClient
          .from('services')
          .insert([{
            name: cleanServiceName,
            duration: option.hours * 60, // Convert hours to minutes
            price: option.price,
            color: '#6a0dad', // Default color
            description: service.description || `${cleanServiceName} service`
          }])
          .select();

        if (createServiceError) {
          console.error('Error creating service:', createServiceError);
          throw createServiceError;
        }

        if (!newService || newService.length === 0) {
          throw new Error('Failed to create service: No data returned');
        }

        serviceData = newService;
        console.log('Created new service:', newService[0]);
      } catch (error) {
        console.error('Service creation failed:', error.message);
        return res.status(400).json({
          error: 'Service creation failed',
          message: error.message,
          details: 'The service could not be found or created. Please contact support.'
        });
      }
    }

    serviceId = serviceData[0].id;
    console.log('Using service ID:', serviceId);

    // Calculate start and end times
    const [year, month, day] = date.split('-').map(Number);
    const [hours, minutes] = time.split(':').map(Number);

    // Create date - handle both local time and UTC appropriately
    // For simplicity, we'll use the ISO string format directly
    const dateStr = `${date}T${time}:00`;
    const startTime = new Date(dateStr);

    console.log('Parsed date components:', { year, month, day, hours, minutes });
    console.log('Date string created:', dateStr);
    console.log('Created start time:', startTime.toISOString());

    // Calculate end time based on service duration or option hours
    const endTime = new Date(startTime);
    endTime.setMinutes(endTime.getMinutes() + (option.hours * 60)); // Convert hours to minutes
    console.log('Created end time:', endTime.toISOString());

    // Prepare booking data
    const bookingRecord = {
      customer_id: customerId,
      service_id: serviceId,
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString(),
      status: service.bookingType === 'Request to Book' ? 'pending' : 'confirmed',
      location,
      notes: message || ''
    };

    console.log('Inserting booking record:', bookingRecord);

    // Create the booking
    const { data: booking, error: bookingError } = await client
      .from('bookings')
      .insert([bookingRecord])
      .select();

    if (bookingError) {
      console.error('Error creating booking:', bookingError);
      throw bookingError;
    }

    console.log('Successfully created booking:', booking[0]);

    // Send notification
    await sendBookingNotification({
      bookingId: booking[0].id,
      customerId,
      status: booking[0].status,
      startTime: startTime.toISOString(),
      serviceName: service.name,
      location
    });

    // Return success response
    return res.status(201).json({
      success: true,
      message: service.bookingType === 'Request to Book'
        ? 'Your booking request has been submitted. We will contact you shortly to confirm.'
        : 'Your booking has been confirmed. A confirmation email has been sent to your email address.',
      booking: booking[0]
    });
  } catch (error) {
    console.error('Error processing booking:', error);
    return res.status(500).json({ error: 'Failed to process booking' });
  }
}
