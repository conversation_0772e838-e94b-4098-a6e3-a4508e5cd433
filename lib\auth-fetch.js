/**
 * Authentication Fetch Helper
 *
 * This module provides utilities for making authenticated API requests
 * in cross-origin environments.
 */
import { supabase } from './supabase';

/**
 * Get the authentication token from various sources
 * @returns {Promise<string|null>} The token or null if not available
 */
export async function getAuthToken() {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 6);
  console.log(`[${requestId}] Getting auth token`);

  // First try to get token from sessionStorage (fastest)
  if (typeof window !== 'undefined' && window.sessionStorage) {
    try {
      const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
      if (cachedToken) {
        try {
          // Check if it's a JSON string or a plain token
          if (cachedToken.startsWith('{')) {
            const tokenData = JSON.parse(cachedToken);
            if (tokenData && tokenData.token) {
              // Check if token is expired
              if (tokenData.expires && tokenData.expires > Date.now()) {
                console.log(`[${requestId}] Using valid token from sessionStorage (JSON format)`);
                return tokenData.token;
              } else {
                console.log(`[${requestId}] Cached token is expired, will refresh`);
                // Continue to refresh the token
              }
            }
          } else {
            // It's a plain token string
            console.log(`[${requestId}] Using token from sessionStorage (string format)`);
            return cachedToken;
          }
        } catch (parseError) {
          console.warn(`[${requestId}] Error parsing token from sessionStorage:`, parseError.message);
          // If parsing fails, it might be a plain token string
          // Use it anyway and let the server validate it
          return cachedToken;
        }
      } else {
        console.log(`[${requestId}] No token found in sessionStorage`);
      }
    } catch (storageError) {
      console.warn(`[${requestId}] Error accessing sessionStorage:`, storageError.message);
    }
  }

  // If no valid token in sessionStorage, try to get from current session
  try {
    console.log(`[${requestId}] Getting token from current session`);
    const client = supabase;

    // Add timeout protection for getSession call
    const sessionPromise = client.auth.getSession();

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Session retrieval timed out after 5 seconds'));
      }, 5000); // 5 second timeout
    });

    // Race the promises
    let sessionResult;
    try {
      sessionResult = await Promise.race([sessionPromise, timeoutPromise]);
    } catch (timeoutError) {
      console.warn(`[${requestId}] Session retrieval timed out:`, timeoutError.message);
      // Continue to refresh attempt
      sessionResult = { data: null };
    }

    const token = sessionResult?.data?.session?.access_token;

    if (token) {
      console.log(`[${requestId}] Successfully retrieved token from current session`);

      // Store in sessionStorage for future use with expiration
      if (typeof window !== 'undefined' && window.sessionStorage) {
        try {
          // Store with expiration time (1 hour from now)
          const tokenData = {
            token: token,
            expires: Date.now() + (60 * 60 * 1000) // 1 hour
          };
          sessionStorage.setItem('oss_auth_token_cache', JSON.stringify(tokenData));
          console.log(`[${requestId}] Cached token in sessionStorage with expiration`);
        } catch (e) {
          console.warn(`[${requestId}] Failed to cache auth token:`, e.message);
        }
      }

      return token;
    } else {
      console.log(`[${requestId}] No token in current session, will try refresh`);
    }
  } catch (sessionError) {
    console.warn(`[${requestId}] Error getting session:`, sessionError.message);
    // Continue to refresh attempt
  }

  // As a last resort, try to refresh the session
  try {
    console.log(`[${requestId}] Attempting to refresh session`);
    const client = supabase;

    // Add timeout protection for refreshSession call
    const refreshPromise = client.auth.refreshSession();

    // Create a timeout promise
    const refreshTimeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Session refresh timed out after 5 seconds'));
      }, 5000); // 5 second timeout
    });

    // Race the promises
    let refreshResult;
    try {
      refreshResult = await Promise.race([refreshPromise, refreshTimeoutPromise]);
    } catch (timeoutError) {
      console.warn(`[${requestId}] Session refresh timed out:`, timeoutError.message);
      return null;
    }

    const { data: refreshData, error: refreshError } = refreshResult;

    if (refreshError) {
      console.warn(`[${requestId}] Session refresh error:`, refreshError.message);
      return null;
    }

    if (!refreshData || !refreshData.session || !refreshData.session.access_token) {
      console.warn(`[${requestId}] Session refresh returned no token`);
      return null;
    }

    const refreshedToken = refreshData.session.access_token;
    console.log(`[${requestId}] Successfully refreshed token`);

    // Store in sessionStorage for future use with expiration
    if (typeof window !== 'undefined' && window.sessionStorage) {
      try {
        // Store with expiration time (1 hour from now)
        const tokenData = {
          token: refreshedToken,
          expires: Date.now() + (60 * 60 * 1000) // 1 hour
        };
        sessionStorage.setItem('oss_auth_token_cache', JSON.stringify(tokenData));
        console.log(`[${requestId}] Cached refreshed token in sessionStorage with expiration`);
      } catch (e) {
        console.warn(`[${requestId}] Failed to cache refreshed auth token:`, e.message);
      }
    }

    return refreshedToken;
  } catch (refreshError) {
    console.warn(`[${requestId}] Error refreshing session:`, refreshError.message);
  }

  console.warn(`[${requestId}] Failed to get authentication token from any source`);
  return null;
}

/**
 * Patch the global fetch function to add authentication headers
 */
export function patchFetch() {
  if (typeof window === 'undefined' || window._authFetchPatched) {
    return; // Already patched or not in browser
  }

  // Store the original fetch function
  const originalFetch = window.fetch;

  // Replace with our patched version
  window.fetch = async function authFetch(url, options = {}) {
    // Skip OneSignal requests to prevent CORS issues
    if (typeof url === 'string' && (
        url.includes('onesignal.com') ||
        url.includes('OneSignal') ||
        url.includes('onesignal')
    )) {
      console.log(`Skipping auth headers for OneSignal request: ${url}`);
      return originalFetch(url, options);
    }

    // Only intercept API requests
    if (typeof url === 'string' && url.includes('/api/')) {
      try {
        // Get the auth token
        const token = await getAuthToken();

        if (token) {
          // Create new options with auth headers
          const newOptions = { ...options };
          newOptions.headers = { ...newOptions.headers };

          // Add Authorization header if not already present
          if (!newOptions.headers.Authorization && !newOptions.headers.authorization) {
            // Ensure token is properly formatted for Bearer authentication
            if (token) {
              // Generate a request ID for logging
              const reqId = Math.random().toString(36).substring(2, 6);

              if (!token.includes(' ')) {
                // Standard case - add Bearer prefix
                newOptions.headers.Authorization = `Bearer ${token}`;
                console.log(`[${reqId}] Added standard Bearer token to request`);
              } else if (token.startsWith('Bearer ')) {
                // Already has Bearer prefix
                newOptions.headers.Authorization = token;
                console.log(`[${reqId}] Using token with existing Bearer prefix`);
              } else {
                // Non-standard token format, but add Bearer prefix anyway
                console.warn(`[${reqId}] Non-standard token format detected, adding Bearer prefix`);
                newOptions.headers.Authorization = `Bearer ${token}`;
              }
            }
          }

          // Add X-Auth-Token header for cross-origin requests
          if (token) {
            // For cross-origin requests, use the raw token without Bearer prefix
            const rawToken = token.startsWith('Bearer ') ? token.substring(7) : token;
            newOptions.headers['X-Auth-Token'] = rawToken;
          }

          // Make the request with the auth headers
          return originalFetch(url, newOptions);
        }
      } catch (error) {
        console.warn('Error adding auth headers to fetch:', error);
      }
    }

    // Fall back to original fetch
    return originalFetch(url, options);
  };

  // Mark as patched
  window._authFetchPatched = true;
  console.log('Fetch patched with auth headers');
}

/**
 * Make an authenticated API request
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} - The response data
 */
export async function authFetch(url, options = {}) {
  // Skip OneSignal requests to prevent CORS issues
  if (typeof url === 'string' && (
      url.includes('onesignal.com') ||
      url.includes('OneSignal') ||
      url.includes('onesignal')
  )) {
    console.log(`Skipping auth headers for OneSignal request: ${url}`);

    // Create options without auth headers
    const newOptions = { ...options };
    newOptions.headers = {
      'Content-Type': 'application/json',
      ...(options.headers || {})
    };

    // Make the request without auth headers
    const response = await fetch(url, newOptions);

    // Parse the response
    let data;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    // Handle error responses
    if (!response.ok) {
      const errorMessage = data.error || data.message || 'Unknown error';
      throw new Error(errorMessage);
    }

    return data;
  }

  // For non-OneSignal requests, proceed with auth headers
  // Get the auth token
  const token = await getAuthToken();

  if (!token) {
    console.warn('No auth token available for request:', url);
  }

  // Create new options with auth headers
  const newOptions = { ...options };
  newOptions.headers = {
    'Content-Type': 'application/json',
    ...newOptions.headers
  };

  // Add Authorization header if not already present and we have a token
  if (token && !newOptions.headers.Authorization && !newOptions.headers.authorization) {
    // Generate a request ID for logging
    const reqId = Math.random().toString(36).substring(2, 6);

    // Ensure token is properly formatted for Bearer authentication
    if (!token.includes(' ')) {
      // Standard case - add Bearer prefix
      newOptions.headers.Authorization = `Bearer ${token}`;
      console.log(`[${reqId}] Added standard Bearer token to authFetch request`);
    } else if (token.startsWith('Bearer ')) {
      // Already has Bearer prefix
      newOptions.headers.Authorization = token;
      console.log(`[${reqId}] Using token with existing Bearer prefix in authFetch`);
    } else {
      // Non-standard token format, but add Bearer prefix anyway
      console.warn(`[${reqId}] Non-standard token format in authFetch, adding Bearer prefix`);
      newOptions.headers.Authorization = `Bearer ${token}`;
    }
  }

  // Add X-Auth-Token header for cross-origin requests if we have a token
  // BUT NOT for OneSignal requests
  if (token) {
    // For cross-origin requests, use the raw token without Bearer prefix
    const rawToken = token.startsWith('Bearer ') ? token.substring(7) : token;
    newOptions.headers['X-Auth-Token'] = rawToken;
  }

  // Make the request
  const response = await fetch(url, newOptions);

  // Parse the response
  let data;
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    data = await response.json();
  } else {
    data = await response.text();
  }

  // Handle error responses
  if (!response.ok) {
    const errorMessage = data.error || data.message || 'Unknown error';
    throw new Error(errorMessage);
  }

  return data;
}

// Apply the patch if we're in a browser environment
if (typeof window !== 'undefined') {
  patchFetch();
}

export default authFetch;
