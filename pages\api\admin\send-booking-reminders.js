import supabase, { getAdminClient } from '@/lib/supabase';
import { sendBookingReminderNotification } from '@/lib/notifications';
import { validateAdminRole } from '@/lib/auth';

/**
 * API endpoint to send booking reminders for bookings scheduled for tomorrow
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if the user is authenticated and is an admin
    // validateAdminRole already uses Supabase auth via getCurrentUserWithToken
    const isAdminUser = await validateAdminRole(req);
    if (!isAdminUser) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Calculate tomorrow's date range
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    const tomorrowStart = new Date(tomorrow);
    tomorrowStart.setHours(0, 0, 0, 0);

    const tomorrowEnd = new Date(tomorrow);
    tomorrowEnd.setHours(23, 59, 59, 999);

    // Use admin client for fetching bookings
    let client;
    try {
      client = getAdminClient();
      console.log("Admin client initialized successfully for fetching bookings.");
    } catch (clientError) {
      console.error("Failed to initialize admin client:", clientError);
      return res.status(500).json({ error: 'Failed to connect to services. Please try again later.' });
    }

    // Get all confirmed bookings for tomorrow
    const { data: bookings, error: bookingsError } = await client
      .from('bookings')
      .select(`
        id,
        customer_id,
        service_id,
        start_time,
        location,
        services:service_id (name)
      `)
      .eq('status', 'confirmed')
      .gte('start_time', tomorrowStart.toISOString())
      .lte('start_time', tomorrowEnd.toISOString());

    if (bookingsError) {
      console.error('Error fetching bookings:', bookingsError);
      return res.status(500).json({ error: 'Failed to fetch bookings' });
    }

    // If no bookings found, return success with count 0
    if (!bookings || bookings.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No bookings found for tomorrow',
        count: 0
      });
    }

    // Send reminders for each booking
    const results = [];
    for (const booking of bookings) {
      const result = await sendBookingReminderNotification({
        bookingId: booking.id,
        customerId: booking.customer_id,
        startTime: booking.start_time,
        serviceName: booking.services.name,
        location: booking.location
      });

      results.push({
        bookingId: booking.id,
        success: result.success,
        error: result.error
      });
    }

    // Count successful and failed reminders
    const successCount = results.filter(r => r.success).length;
    const failedCount = results.filter(r => !r.success).length;

    return res.status(200).json({
      success: true,
      message: `Sent ${successCount} booking reminders, ${failedCount} failed`,
      count: bookings.length,
      successCount,
      failedCount,
      results
    });
  } catch (error) {
    console.error('Error sending booking reminders:', error);
    // Check if the error is from client initialization
    if (error.message && (
      error.message.includes('Failed to create admin client') ||
      error.message.includes('SUPABASE_SERVICE_ROLE_KEY')
    )) {
      return res.status(503).json({ error: 'Service temporarily unavailable. Please try again later.' });
    }
    return res.status(500).json({ error: 'Failed to send booking reminders' });
  }
}
